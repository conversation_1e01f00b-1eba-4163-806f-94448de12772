import * as Cesium from "cesium";
import bus from "@/utils/bus";
import { v4 as uuid4 } from "uuid";
import tool from "../tool/tool";
import { svgCode } from "@/components/point/svgSource";

class pointHandler {
  /**
   * @description: 添加单个广告牌实体点位
   * @param {any} data
   * @return {*}
   */
  static addOneIconPoint(param: any, iconUrl?: any) {
    const viewer = window.viewer;
    let handler = null;

    // bus.on("mouseMove", (res:any)=>{ 
    //   console.log(res.handler);
    //   handler = res.handler
    //   });
    //   if (handler) {
        
    //     handler.destroy()
    //   }

    let pointEntity = new Cesium.Entity({
      id: param.id || uuid4(),
      name: param.name || "",
      position: param.position,
      // label:{
      //   text:'ewwww'
      // },
      billboard: {
        image: param?.iconUrl || tool.svgToDataUri(svgCode.location.value),
        // image: param?.iconUrl || require("@/assets/images/point/default.png"), //10.11
        width: 32,
        height: 32,
        scale: 1.0,
        // rotation:95.123,
        sizeInMeters: false,
        verticalOrigin:param?.label?.verticalOrigin || Cesium.VerticalOrigin.CENTER,
        horizontalOrigin: param?.label?.horizontalOrigin || Cesium.HorizontalOrigin.CENTER,
        distanceDisplayCondition: param?.label?.distanceDisplayCondition || new Cesium.DistanceDisplayCondition(0, 3000000), //默认300万米高度可以看见
        disableDepthTestDistance: param?.label?.disableDepthTestDistance || 10000,
        heightReference:param?.label?.heightReference ||  Cesium.HeightReference.CLAMP_TO_GROUND,
        show: true,
      },
      properties: {
        position: param.position,
        type: param?.type || "",
        isBillboard: true, // 是否是广告牌标志  必传值（true 或 false）
        isRightMenu: true, // 是否弹出右键菜单  必传值（true 或 false）
        isEdit: true, // 是否可编辑  必传值（true 或 false）
        name: param?.name || "",
      },
    });
    if (param.hasOwnProperty("label")) {
      pointEntity.label = param.label;
    }
    viewer.entities.add(pointEntity);
  }
  static currentPosition(data: any) {
    window.viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(data.lon, data.lat, 0),
      name: data.name,
      id: data.name,
      point: {
        show: true, //是否展示
        pixelSize: 10, //点的大小
        color: Cesium.Color.fromCssColorString("#0F89F5"), //颜色
        outlineColor: Cesium.Color.WHITE, //边框颜色
        outlineWidth: 2, //边框宽度
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
          0,
          1000000
        ),
      },

      properties: {
        position: Cesium.Cartesian3.fromDegrees(data.lon, data.lat),
        // type: data.type,
      },
    });
  }
  /**
   * @description: 添加绘制点和提示框
   * @param {any} obj 绘制数据
   * @param {*} flag 是否绘制删除按钮标志
   * @return {*}
   */
  static addMeasureInfoPoint(obj: any, flag = false) {
    let _labelEntity;
    if (flag) {
      _labelEntity = new Cesium.Entity({
        position: obj.position,
        id: obj.id,
        name: "closeDataSource",
        billboard: {
          image: require("@/assets/images/icon/close.png"),
          scale: 1.0,
          width: 16, //设置图片大小
          height: 16, //设置图片大小
          eyeOffset: new Cesium.Cartesian3(0.0, 0.0, 0.0),
          pixelOffset: new Cesium.Cartesian2(0, 0),
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        properties: {
          id: obj.id,
          type: "closeDataSource",
          position: obj.position,
        },
      });
    } else if (obj.hasOwnProperty("point")) {
      _labelEntity = new Cesium.Entity({
        position: obj.position,
        point: {
          outlineColor: obj.point.outlinrColor,
          color: obj.point.color,
          pixelSize: obj.point.pixelSize,
          outlineWidth: obj.point.outlineWidth,
          distanceDisplayCondition: obj.point.distanceDisplayCondition,
        },
      });
    } else {
      _labelEntity = new Cesium.Entity({
        position: obj.position,
      });
    }

    if (obj.hasOwnProperty("label")) {
      _labelEntity.label = {
        text: obj.label.text,
        show: obj.label.show,
        showBackground: obj.label.showBackground,
        font: obj.label.font,
        distanceDisplayCondition: obj.label.distanceDisplayCondition,
        pixelOffset: obj.label.pixelOffset, //left top
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        disableDepthTestDistance: Number.POSITIVE_INFINITY, //防止模型被覆盖
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, //设置 HeightReference 高度参考类型为 CLAMP_TO_GROUND 贴地类型
      };
    }
    if (obj.hasOwnProperty("entity")) {
      obj.entity.entities.add(_labelEntity);
    }
  }
}
export default pointHandler;
