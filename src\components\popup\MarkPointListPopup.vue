<template>
  <!-- 标记点列表弹窗 -->
  <el-dialog
    v-model="isShow"
    :modal="false"
    :title="dialogTitle"
    width="1020px"
    :close-on-click-modal="false"
    :show-close="true"
    @close="close"
    draggable
  >
    <div class="mark-point-dialog">
      <!-- 只有在显示所有标记点时才显示搜索框和按钮组 -->
      <div class="search-box" v-if="!showSinglePoint">
        <div class="search-container">
          <el-input
            v-model="searchText"
            placeholder="搜索标记点"
            clearable
            size="large"
          >
            <!-- <template #prefix>
              <i class="iconfont gis-sousuo"></i>
            </template> -->
          </el-input>
          <div class="button-group">
            <el-button
              :type="showAllMarkers ? 'warning' : 'primary'"
              @click="toggleAllMarkers"
              plain
            >
              {{ showAllMarkers ? "隐藏所有点" : "显示所有点" }}
            </el-button>
            <el-upload
              class="upload-btn"
              action=""
              :auto-upload="false"
              :show-file-list="false"
              accept=".json"
              :on-change="handleFileChange"
            >
              <el-button type="success" plain>
                <i class="iconfont gis-shangchuan" style="margin-right: 5px"></i
                >导入标记点
              </el-button>
            </el-upload>
          </div>
        </div>
      </div>
      <div class="point-list">
        <el-table :data="paginatedPoints" style="width: 100%" height="400px">
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column
            prop="name"
            label="名称"
            width="150"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div style="display: flex; align-items: center">
                {{ scope.row.name }}
                <el-tag
                  v-if="scope.row.isImported"
                  type="info"
                  size="small"
                  effect="plain"
                  style="margin-left: 5px"
                >
                  导入
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="类型" width="120">
            <template #default="scope">
              <el-tag
                disable-transitions
                :color="
                  MarkerTypeColorMap[
                    scope.row.type as keyof typeof MarkerTypeColorMap
                  ]
                "
                style="color: white"
              >
                <i class="iconfont gis-biaoji" style="font-size: 12px"></i>
                {{ getMarkerTypeName(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column label="标记" width="80">
            <template #default="scope">
              {{ scope.row.flag || '无' }}
            </template>
          </el-table-column> -->
          <el-table-column label="位置" show-overflow-tooltip>
            <template #default="scope">
              {{ formatPosition(scope.row.position) }}
            </template>
          </el-table-column>
          <el-table-column label="偏移量" width="150">
            <template #default="scope">
              {{ formatOffset(scope.row.offset) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="380">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                plain
                @click.stop="viewDetail(scope.row)"
              >
                详情
              </el-button>
              <el-button
                :type="isMarkerVisible(scope.row.id) ? 'warning' : 'success'"
                plain
                size="small"
                @click.stop="toggleMarkerVisibility(scope.row)"
              >
                {{ isMarkerVisible(scope.row.id) ? "隐藏" : "显示" }}
              </el-button>
              <el-button
                type="danger"
                plain
                size="small"
                @click.stop="deletePoint(scope.row.id)"
              >
                删除
              </el-button>
              <el-button
                type="primary"
                plain
                size="small"
                @click.stop="flyToPoint(scope.row, true)"
              >
                定位
              </el-button>
              <el-button
                type="info"
                plain
                size="small"
                @click.stop="openSettingsDialog(scope.row)"
              >
                设置
              </el-button>
              <el-button
                type="primary"
                plain
                size="small"
                @click.stop="openChartSelectionDialog(scope.row)"
                :disabled="scope.row.isImported"
                :title="
                  scope.row.isImported
                    ? '导入的标记点不支持图表功能'
                    : '查看图表'
                "
              >
                分析
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 - 只有在显示所有标记点时才显示 -->
        <div class="pagination-container" v-if="!showSinglePoint">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="filteredPoints.length"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </el-dialog>

  <!-- 详细信息弹窗 -->
  <el-dialog
    v-model="detailDialogVisible"
    title="标记点详细信息"
    width="400px"
    append-to-body
    center
  >
    <div v-if="selectedPoint">
      <div class="detail-item">
        <span class="label">名称：</span>
        <span>
          {{ selectedPoint.name }}
          <el-tag
            v-if="selectedPoint.isImported"
            type="info"
            size="small"
            effect="plain"
            style="margin-left: 5px"
          >
            导入
          </el-tag>
        </span>
      </div>
      <div class="detail-item">
        <span class="label">类型：</span>
        <span>
          <el-tag
            disable-transitions
            :color="
              MarkerTypeColorMap[
                selectedPoint.type as keyof typeof MarkerTypeColorMap
              ]
            "
            style="color: white"
          >
            {{ getMarkerTypeName(selectedPoint.type) }}
          </el-tag>
        </span>
      </div>
      <div class="detail-item">
        <span class="label">位置：</span>
        <span>{{ formatPosition(selectedPoint.position) }}</span>
      </div>
      <div class="detail-item">
        <span class="label">偏移量：</span>
        <span>{{ formatOffset(selectedPoint.offset) }}</span>
      </div>
      <div class="detail-item">
        <span class="label">创建时间：</span>
        <span>{{ formatDate(selectedPoint.createTime) }}</span>
      </div>
      <div class="detail-item">
        <span class="label">标记：</span>
        <span>{{ selectedPoint.flag || "无" }}</span>
      </div>
      <div class="detail-item">
        <span class="label">描述：</span>
        <span>{{ selectedPoint.description || "无" }}</span>
      </div>

      <!-- 过去一周数据 -->
      <!-- <div class="detail-item" v-if="selectedPoint && (selectedPoint as any).weeklyData && (selectedPoint as any).weeklyData.length > 0">
        <span class="label">过去一周数据：</span>
        <div class="weekly-data">
          <div v-for="(item, index) in (selectedPoint as any).weeklyData" :key="index" class="weekly-data-item">
            <div class="date">{{ item.date }}</div>
            <div class="value" :style="{ height: Math.min(item.value, 100) + 'px' }">{{ item.value }}</div>
          </div>
        </div>
      </div> -->
    </div>
    <!-- <template #footer>
      <span class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="flyToPoint(selectedPoint, true)">
          定位并关闭
        </el-button>
      </span>
    </template> -->
  </el-dialog>

  <EchartCard ref="echartCard" />

  <!-- 设置弹窗 -->
  <el-dialog
    v-model="settingsDialogVisible"
    title="标记点设置"
    width="500px"
    append-to-body
    center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div v-if="editingPoint">
      <el-form
        ref="settingsFormRef"
        :model="editingForm"
        :rules="formRules"
        label-width="100px"
        status-icon
      >
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="editingForm.name"
            placeholder="请输入标记点名称"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="editingForm.type" placeholder="请选择标记点类型">
            <el-option
              v-for="(name, type) in MarkerTypeNameMap"
              :key="type"
              :label="name"
              :value="type"
            >
              <div style="display: flex; align-items: center">
                <div
                  :style="{
                    backgroundColor:
                      MarkerTypeColorMap[
                        type as keyof typeof MarkerTypeColorMap
                      ],
                    width: '16px',
                    height: '16px',
                    borderRadius: '4px',
                    marginRight: '8px',
                  }"
                ></div>
                <span>{{ name }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="标记" prop="flag">
          <el-input v-model="editingForm.flag" placeholder="请输入标记" maxlength="50"></el-input>
        </el-form-item> -->
        <el-form-item label="经度" prop="position.lon">
          <el-input-number
            v-model="editingForm.position.lon"
            :precision="6"
            :step="0.000001"
            :min="-180"
            :max="180"
            style="width: 100%"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="纬度" prop="position.lat">
          <el-input-number
            v-model="editingForm.position.lat"
            :precision="6"
            :step="0.000001"
            :min="-90"
            :max="90"
            style="width: 100%"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="高度" prop="position.alt">
          <el-input-number
            v-model="editingForm.position.alt"
            :precision="2"
            :step="1"
            :min="0"
            style="width: 100%"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="X偏移" prop="offset.x">
          <el-input-number
            v-model="editingForm.offset.x"
            :precision="2"
            :step="0.1"
            style="width: 100%"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="Y偏移" prop="offset.y">
          <el-input-number
            v-model="editingForm.offset.y"
            :precision="2"
            :step="0.1"
            style="width: 100%"
            controls-position="right"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="editingForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入标记点描述"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item
          label="图标类型"
          prop="description"
          v-if="editingForm?.flag === 'tw'"
        >
          <el-select v-model="iconType" placeholder="请选择">
            <el-option
              v-for="item in iconTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div style="text-align: right; margin-top: 20px">
        <el-button @click="settingsDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="formLoading" @click="saveSettings"
          >保存</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import * as Cesium from "cesium";

import { computed, reactive, ref, watch } from "vue";

import { ElMessage, ElMessageBox, UploadFile } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";

import { type MarkPoint, useMarkPointsStore } from "@/store/markPoints";

import pointImage from "@/assets/images/point/default.png";

import useEntityHighlight from "@/hooks/useEntityHighlight";

import { canvasToImageUrl, generateChartByType } from "@/utils/chartGenerator";

import EchartCard from "./EchartCard.vue";
import { MarkerTypeColorMap, MarkerTypeNameMap } from "@/constants/markerTypes";

const echartCard = ref<InstanceType<typeof EchartCard> | null>(null);
const iconType = ref("default");
const iconTypeList = [
  { value: "default", label: "默认图标" },
  { value: "pie", label: "饼图" },
  { value: "bar", label: "柱状图" },
];

/**
 * 使用图表更新实体
 * @param entity Cesium实体
 * @param chartType 图表类型
 */
const updateEntityWithChart = (entity: Cesium.Entity, chartType: string) => {
  // 如果是default类型，恢复默认图标
  if (chartType === "default") {
    if (entity.billboard) {
      // 获取对应类型的颜色
      const point = markPointsStore.getMarkPointById(entity.id);
      if (!point) return;

      const iconColor = Cesium.Color.fromCssColorString(
        MarkerTypeColorMap[point.type as keyof typeof MarkerTypeColorMap] ||
          "#FFFFFF",
      );

      // 恢复默认图标
      entity.billboard.image = new Cesium.ConstantProperty(pointImage);
      entity.billboard.color = new Cesium.ConstantProperty(iconColor);
      entity.billboard.width = new Cesium.ConstantProperty(32);
      entity.billboard.height = new Cesium.ConstantProperty(32);
    }
    return;
  }

  // 生成图表
  const canvas = generateChartByType(chartType);
  if (!canvas) {
    console.error("生成图表失败");
    return;
  }

  // 将canvas转换为图片URL
  const imageUrl = canvasToImageUrl(canvas);

  // 更新实体的billboard
  if (entity.billboard) {
    entity.billboard.image = new Cesium.ConstantProperty(imageUrl);
    entity.billboard.color = new Cesium.ConstantProperty(Cesium.Color.WHITE);
    entity.billboard.width = new Cesium.ConstantProperty(132);
    entity.billboard.height = new Cesium.ConstantProperty(132);
  }
};

// 使用实体高亮钩子
const { highlightEntity, stopHighlight } = useEntityHighlight();

// 状态变量
const isShow = ref(false);
const searchText = ref("");
const detailDialogVisible = ref(false);
const selectedPoint = ref<MarkPoint | null>(null);

// 设置弹窗相关
const settingsDialogVisible = ref(false);
const editingPoint = ref<MarkPoint | null>(null);
const editingForm = reactive({
  name: "",
  type: "",
  flag: "",
  position: {
    lon: 0,
    lat: 0,
    alt: 0,
  },
  offset: {
    x: 0,
    y: 0,
  },
  description: "",
});

// 表单引用和验证规则
const settingsFormRef = ref<FormInstance>();
const formLoading = ref(false);
const formRules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入标记点名称", trigger: "blur" },
    { min: 1, max: 50, message: "名称长度应在1-50个字符之间", trigger: "blur" },
  ],
  type: [{ required: true, message: "请选择标记点类型", trigger: "change" }],
  "position.lon": [
    { required: true, message: "请输入经度", trigger: "blur" },
    {
      validator: (_rule, value, callback) => {
        if (value < -180 || value > 180) {
          callback(new Error("经度范围应在-180到180之间"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  "position.lat": [
    { required: true, message: "请输入纬度", trigger: "blur" },
    {
      validator: (_rule, value, callback) => {
        if (value < -90 || value > 90) {
          callback(new Error("纬度范围应在-90到90之间"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  "position.alt": [
    {
      validator: (_rule, value, callback) => {
        if (value < 0) {
          callback(new Error("高度不能为负数"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);

// 控制是否显示所有标记点 - 仅用于UI状态，实际显示状态存储在store中
const showAllMarkers = ref(false);

// 当前选中的标记点ID
const selectedPointId = ref<string | null>(null);

// 控制是否只显示单个标记点
const showSinglePoint = ref(false);

// 获取标记点存储
const markPointsStore = useMarkPointsStore();

// 根据搜索文本和显示模式过滤标记点
const filteredPoints = computed(() => {
  // 如果只显示单个标记点，且有选中的标记点ID
  if (showSinglePoint.value && selectedPointId.value) {
    const point = markPointsStore.getMarkPointById(selectedPointId.value);
    return point ? [point] : [];
  }

  // 否则，根据搜索文本过滤
  return markPointsStore.searchMarkPointsByName(searchText.value);
});

// 监听搜索文本变化，重置页码并更新标记点显示状态
watch(searchText, () => {
  currentPage.value = 1;
  updateMarkersVisibility();
});

// 分页后的数据
const paginatedPoints = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredPoints.value.slice(start, end);
});

// 弹窗标题
const dialogTitle = computed(() => {
  // 如果只显示单个标记点，且有选中的标记点ID
  if (showSinglePoint.value && selectedPointId.value) {
    const point = markPointsStore.getMarkPointById(selectedPointId.value);
    if (point) {
      return `标记点: ${point.name}`;
    }
  }

  // 否则，显示默认标题
  return "标记点列表";
});

// 格式化位置信息
const formatPosition = (position: {
  lon: number;
  lat: number;
  alt: number;
}) => {
  return `经度:${position.lon.toFixed(4)}°,纬度:${position.lat.toFixed(4)}°`;
};

// 格式化偏移量信息
const formatOffset = (offset?: { x: number; y: number }) => {
  if (!offset) return "无偏移";
  return `X: ${offset.x.toFixed(2)}, Y: ${offset.y.toFixed(2)}`;
};

// 格式化日期
const formatDate = (date: Date) => {
  if (!(date instanceof Date)) {
    date = new Date(date);
  }
  return date.toLocaleString();
};

// 获取标记点类型名称
const getMarkerTypeName = (type: string) => {
  return MarkerTypeNameMap[type as keyof typeof MarkerTypeNameMap] || type;
};

// 查看详细信息
const viewDetail = (point: MarkPoint) => {
  selectedPoint.value = point;
  detailDialogVisible.value = true;
};

// 删除标记点
const deletePoint = (id: string) => {
  ElMessageBox.confirm("确定要删除这个标记点吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 从地图上移除标记点
      const viewer = window.viewer;
      const entity = viewer.entities.getById(id);
      if (entity) {
        viewer.entities.remove(entity);
      }

      // 从存储中删除标记点
      markPointsStore.deleteMarkPoint(id);
      ElMessage({
        type: "success",
        message: "删除成功",
      });
      isShow.value = false;
    })
    .catch(() => {
      // 取消删除
    });
};

// 更新地图上所有标记点的显示状态
const updateMarkersVisibility = () => {
  const viewer = window.viewer;
  if (!viewer) return;

  // 获取所有标记点
  const allPoints = markPointsStore.getAllMarkPoints;

  allPoints.forEach((point) => {
    const entity = viewer.entities.getById(point.id);
    if (entity) {
      // 使用store中存储的可见性状态
      // 如果point.visible未定义，则使用showAllMarkers的值
      entity.show =
        typeof point.visible === "boolean"
          ? point.visible
          : showAllMarkers.value;
    }
  });
};

// 切换标记点显示状态
const toggleMarkerVisibility = (point: MarkPoint) => {
  const viewer = window.viewer;
  if (!viewer) return;

  // 获取当前可见状态（从实体或从store）
  const entity = viewer.entities.getById(point.id);
  const currentVisibility = entity ? entity.show : point.visible || false;

  // 切换显示状态
  const newVisibility = !currentVisibility;

  // 更新store中的可见性状态
  markPointsStore.updateMarkPoint(point.id, { visible: newVisibility });

  // 如果实体存在，更新其显示状态
  if (entity) {
    entity.show = newVisibility;

    // 如果显示了标记点，则飞行到该位置，但不高亮显示
    if (newVisibility) {
      // 飞行到标记点位置
      viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          point.position.lon,
          point.position.lat,
          10000.0,
        ),
        duration: 1.5,
        complete: () => {
          // 飞行完成后设置为选中状态并高亮显示
          viewer.selectedEntity = entity;
          // 在飞行完成后应用高亮效果
          highlightEntity(entity);
        },
      });
    }
  }

  // 关闭弹窗
  isShow.value = false;
};

// 飞行到标记点位置
const flyToPoint = (
  point: MarkPoint | null,
  closeMainDialog: boolean = false,
  shouldHighlight: boolean = true,
) => {
  if (!point) return;

  const viewer = window.viewer;

  // 确保标记点可见
  const entity = viewer.entities.getById(point.id);
  if (entity) {
    entity.show = true;

    // 同时更新store中的可见性状态
    markPointsStore.updateMarkPoint(point.id, { visible: true });

    // 只有在需要高亮时才应用高亮效果
    if (shouldHighlight) {
      highlightEntity(entity);
    }
  }

  // 飞行到标记点位置
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      point.position.lon,
      point.position.lat,
      10000.0,
    ),
    duration: 1.5,
    complete: () => {
      // 飞行完成后设置为选中状态
      if (entity) {
        viewer.selectedEntity = entity;
      }
    },
  });

  // 关闭详细信息弹窗
  detailDialogVisible.value = false;

  // 如果需要，关闭主弹窗
  if (closeMainDialog) {
    isShow.value = false;
  }
};

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

// 处理每页显示数量变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  // 重置当前页码，避免数据不足导致空页
  if (
    currentPage.value > Math.ceil(filteredPoints.value.length / pageSize.value)
  ) {
    currentPage.value = 1;
  }
};

// 切换所有标记点的显示状态
const toggleAllMarkers = () => {
  // 切换显示状态
  showAllMarkers.value = !showAllMarkers.value;

  // 更新store中所有标记点的可见性状态
  // 遍历所有标记点并更新可见性
  markPointsStore.getAllMarkPoints.forEach((point) => {
    markPointsStore.updateMarkPoint(point.id, {
      visible: showAllMarkers.value,
    });
  });

  // 更新地图上的标记点显示状态
  updateMarkersVisibility();

  // 如果显示所有标记点，则调整视图以包含所有标记点
  if (showAllMarkers.value) {
    // 获取所有标记点
    const allPoints = markPointsStore.getAllMarkPoints;

    // 如果有标记点，则调整视图以包含所有标记点
    if (allPoints.length > 0) {
      const viewer = window.viewer;
      if (viewer) {
        // 创建一个矩形，包含所有标记点
        let west = 180;
        let south = 90;
        let east = -180;
        let north = -90;

        // 计算包含所有标记点的矩形范围
        allPoints.slice(1).forEach((point) => {
          const lon = point.position.lon;
          const lat = point.position.lat;

          west = Math.min(west, lon);
          south = Math.min(south, lat);
          east = Math.max(east, lon);
          north = Math.max(north, lat);
        });

        // 添加一些边距
        const padding = 0.1; // 约10公里
        west -= padding;
        south -= padding;
        east += padding;
        north += padding;

        // 创建矩形
        const rectangle = Cesium.Rectangle.fromDegrees(
          west,
          south,
          east,
          north,
        );

        // 飞行到矩形范围
        viewer.camera.flyTo({
          destination: rectangle,
          duration: 1.5,
        });
      }
    }
  }
};

// 检查标记点是否可见
const isMarkerVisible = (id: string): boolean => {
  // 首先从store中获取标记点
  const point = markPointsStore.getMarkPointById(id);
  if (!point) return false;

  // 如果标记点在store中有明确的可见性状态，则使用它
  if (typeof point.visible === "boolean") {
    return point.visible;
  }

  // 否则检查实体的可见性
  const viewer = window.viewer;
  if (!viewer) return false;

  const entity = viewer.entities.getById(id);
  return entity ? entity.show : false;
};

// 打开设置弹窗
const openSettingsDialog = (point: MarkPoint) => {
  editingPoint.value = point;

  // 复制点的数据到表单
  editingForm.name = point.name;
  editingForm.type = point.type;
  editingForm.position.lon = point.position.lon;
  editingForm.position.lat = point.position.lat;
  editingForm.position.alt = point.position.alt || 0;
  editingForm.flag = point?.flag || "";

  // 初始化偏移量
  if (point.offset) {
    editingForm.offset.x = point.offset.x;
    editingForm.offset.y = point.offset.y;
  } else {
    editingForm.offset.x = 0;
    editingForm.offset.y = 0;
  }

  editingForm.description = point.description || "";

  // 初始化图标类型
  iconType.value = point.chartType || "default";

  // 显示弹窗
  settingsDialogVisible.value = true;
};

// 保存设置
const saveSettings = () => {
  if (!editingPoint.value || !settingsFormRef.value) return;

  // 保存当前编辑点的ID，以防在异步操作过程中editingPoint.value变为null
  const editingPointId = editingPoint.value.id;

  // 表单验证
  settingsFormRef.value.validate(async (valid, fields) => {
    if (!valid) {
      // 表单验证失败
      console.error("表单验证失败:", fields);
      ElMessage({
        type: "error",
        message: "请正确填写表单信息",
      });
      return;
    }

    try {
      // 设置加载状态
      formLoading.value = true;

      const viewer = window.viewer;
      if (!viewer) {
        throw new Error("地图实例不存在");
      }

      // 检查经纬度是否有效
      if (editingForm.position.lon < -180 || editingForm.position.lon > 180) {
        throw new Error("经度范围应在-180到180之间");
      }

      if (editingForm.position.lat < -90 || editingForm.position.lat > 90) {
        throw new Error("纬度范围应在-90到90之间");
      }

      if (editingForm.position.alt < 0) {
        throw new Error("高度不能为负数");
      }

      // 保存图标类型（如果flag为tw）
      let chartType = "default";
      if (editingForm.flag === "tw") {
        chartType = iconType.value;
      } else {
        // 如果flag不是tw，确保图标类型为default
        chartType = "default";
      }

      // 更新store中的标记点数据
      markPointsStore.updateMarkPoint(editingPointId, {
        name: editingForm.name,
        type: editingForm.type,
        flag: editingForm.flag, // 更新flag属性
        position: {
          lon: editingForm.position.lon,
          lat: editingForm.position.lat,
          alt: editingForm.position.alt,
        },
        offset: {
          x: editingForm.offset.x,
          y: editingForm.offset.y,
        },
        description: editingForm.description,
        chartType: chartType, // 添加图表类型
      });

      // 更新地图上的标记点
      const entity = viewer.entities.getById(editingPointId);
      if (entity) {
        try {
          // 更新位置，考虑偏移量
          let position = Cesium.Cartesian3.fromDegrees(
            editingForm.position.lon,
            editingForm.position.lat,
            editingForm.position.alt,
          );

          // 应用偏移量
          if (editingForm.offset) {
            // 在实际应用中，这里需要根据具体需求实现偏移量的应用逻辑
            // 这里我们使用一个简单的方法来模拟偏移效果

            // 获取东北上坐标系
            const eastNorthUpMatrix =
              Cesium.Transforms.eastNorthUpToFixedFrame(position);

            // 创建偏移向量 (二维，Z设为0)
            const offsetVector = new Cesium.Cartesian3(
              editingForm.offset.x * 10, // 放大偏移效果，使其更明显
              editingForm.offset.y * 10,
              0, // 不使用Z偏移
            );

            // 应用偏移
            const offsetPosition = new Cesium.Cartesian3();
            Cesium.Matrix4.multiplyByPoint(
              eastNorthUpMatrix,
              offsetVector,
              offsetPosition,
            );

            // 更新位置
            position = offsetPosition;
          }

          entity.position = new Cesium.ConstantPositionProperty(position);

          // 更新颜色
          const iconColor = Cesium.Color.fromCssColorString(
            MarkerTypeColorMap[
              editingForm.type as keyof typeof MarkerTypeColorMap
            ] || "#FFFFFF",
          );

          // 根据图标类型更新billboard
          if (entity.billboard) {
            // 如果是tw标记且选择了非default图标类型，则更新图标
            if (editingForm.flag === "tw" && chartType !== "default") {
              // 使用图表生成器生成图表
              updateEntityWithChart(entity, chartType);
            } else {
              // 使用默认图标
              entity.billboard.image = new Cesium.ConstantProperty(pointImage);
              entity.billboard.color = new Cesium.ConstantProperty(iconColor);
            }
          }

          if (entity.label) {
            entity.label.text = new Cesium.ConstantProperty(editingForm.name);
            entity.label.backgroundColor = new Cesium.ConstantProperty(
              iconColor.withAlpha(0.7),
            );
          }

          // 更新属性
          if (entity.properties) {
            entity.properties.type = editingForm.type;
            entity.properties.name = editingForm.name;
            entity.properties.description = editingForm.description;
            entity.properties.chartType = chartType;
          }

          // 不再应用高亮效果
        } catch (error) {
          console.error("更新地图实体失败:", error);
          throw new Error("更新地图实体失败");
        }
      } else {
        // 如果实体不存在，重新创建
        checkAndRestoreMarkPoints();
      }

      // 关闭弹窗
      settingsDialogVisible.value = false;

      // 显示成功消息
      ElMessage({
        type: "success",
        message: "标记点设置已更新",
      });
    } catch (error) {
      // 处理错误
      console.error("保存设置失败:", error);
      ElMessage({
        type: "error",
        message: error instanceof Error ? error.message : "保存设置失败",
      });
    } finally {
      // 无论成功还是失败，都关闭加载状态
      formLoading.value = false;
    }
  });
};

// 检查并恢复标记点实体
const checkAndRestoreMarkPoints = () => {
  const viewer = window.viewer;
  if (!viewer) return;

  // 获取所有标记点
  const allPoints = markPointsStore.getAllMarkPoints;

  // 检查每个标记点是否存在于地图上
  allPoints.forEach((point) => {
    const entity = viewer.entities.getById(point.id);

    // 如果标记点不存在于地图上，则重新添加
    if (!entity) {
      // 添加到地图上
      // 计算位置，考虑偏移量
      let position = Cesium.Cartesian3.fromDegrees(
        point.position.lon,
        point.position.lat,
        point.position.alt || 0,
      );

      // 如果有偏移量，应用偏移
      if (point.offset) {
        // 获取东北上坐标系
        const eastNorthUpMatrix =
          Cesium.Transforms.eastNorthUpToFixedFrame(position);

        // 创建偏移向量 (二维，Z设为0)
        const offsetVector = new Cesium.Cartesian3(
          point.offset.x * 10, // 放大偏移效果，使其更明显
          point.offset.y * 10,
          0, // 不使用Z偏移
        );

        // 应用偏移
        const offsetPosition = new Cesium.Cartesian3();
        Cesium.Matrix4.multiplyByPoint(
          eastNorthUpMatrix,
          offsetVector,
          offsetPosition,
        );

        // 更新位置
        position = offsetPosition;

        // console.log(`应用偏移量: X=${point.offset.x}, Y=${point.offset.y}`);
      }

      // 获取对应类型的颜色，如果没有则使用默认颜色
      const iconColor = Cesium.Color.fromCssColorString(
        MarkerTypeColorMap[point.type as keyof typeof MarkerTypeColorMap] ||
          "#FFFFFF",
      );

      // 创建实体
      const entity = viewer.entities.add({
        id: point.id,
        name: point.name,
        position: position,
        billboard: {
          image: pointImage,
          width: 32,
          height: 32,
          scale: 1.0,
          color: iconColor,
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
            0,
            3000000,
          ),
          disableDepthTestDistance: 10000,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: point.name,
          font: "12px sans-serif",
          fillColor: Cesium.Color.WHITE,
          backgroundColor: iconColor.withAlpha(0.7),
          showBackground: true,
          style: Cesium.LabelStyle.FILL,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          pixelOffset: new Cesium.Cartesian2(0, -10),
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
            0,
            3000000,
          ),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        properties: {
          position,
          type: point.type,
          isBillboard: true,
          isRightMenu: true,
          isEdit: true,
          name: point.name,
          description: point.description,
        },
        show:
          typeof point.visible === "boolean"
            ? point.visible
            : showAllMarkers.value, // 使用存储的可见性状态
      });

      // 如果是tw标记且有图表类型，则更新图标
      if (point.flag === "tw" && point.chartType) {
        updateEntityWithChart(entity, point.chartType);
      }
    }
  });

  // 更新标记点显示状态
  updateMarkersVisibility();
};

// 打开弹窗
const open = (pointId?: string) => {
  isShow.value = true;

  // 重置分页状态
  currentPage.value = 1;

  // 重置搜索文本
  searchText.value = "";

  // 检查并恢复标记点实体
  checkAndRestoreMarkPoints();

  // 如果提供了标记点ID，则只显示该标记点
  if (pointId) {
    // 设置当前选中的标记点ID
    selectedPointId.value = pointId;

    // 设置只显示单个标记点
    showSinglePoint.value = true;

    const point = markPointsStore.getMarkPointById(pointId);
    if (point) {
      // 确保该标记点在地图上可见并飞行到该位置
      const viewer = window.viewer;
      if (viewer) {
        const entity = viewer.entities.getById(pointId);
        if (entity) {
          // 无论 showAllMarkers 的值如何，都确保该标记点可见
          entity.show = true;

          // 同时更新store中的可见性状态
          markPointsStore.updateMarkPoint(pointId, { visible: true });
        }
      }

      // 飞行到该标记点，但不高亮显示（点击时不高亮）
      flyToPoint(point, false, false);
    }
  } else {
    // 如果没有提供标记点ID，则显示所有标记点
    selectedPointId.value = null;
    showSinglePoint.value = false;

    // 更新地图上标记点的显示状态
    updateMarkersVisibility();
  }
};

// 关闭弹窗
const close = () => {
  isShow.value = false;
  detailDialogVisible.value = false;

  // 停止所有标记点的高亮效果
  const viewer = window.viewer;
  if (viewer) {
    const entities = viewer.entities.values;
    entities.forEach((entity: Cesium.Entity) => {
      if ((entity as any)._highlightAnimation) {
        stopHighlight(entity);
      }
    });
  }
};

// 处理文件上传
const handleFileChange = (uploadFile: UploadFile) => {
  const file = uploadFile.raw;
  if (!file) {
    ElMessage.error("文件获取失败");
    return;
  }

  // 检查文件类型
  if (file.type !== "application/json" && !file.name.endsWith(".json")) {
    ElMessage.error("请上传JSON格式的文件");
    return;
  }

  // 读取文件内容
  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      const content = e.target?.result as string;
      const jsonData = JSON.parse(content);

      // 验证并导入数据
      validateAndImportData(jsonData);
    } catch (error) {
      console.error("解析JSON文件失败:", error);
      ElMessage.error("解析JSON文件失败，请确保文件格式正确");
    }
  };

  reader.onerror = () => {
    ElMessage.error("读取文件失败");
  };

  reader.readAsText(file);
};

// 验证并导入标记点数据
const validateAndImportData = (data: any) => {
  // 检查数据是否为数组
  if (!Array.isArray(data)) {
    ElMessage.error("导入失败：数据格式不正确，应为标记点数组");
    return;
  }

  // 验证每个标记点数据
  const validPoints: Partial<MarkPoint>[] = [];
  const invalidPoints: any[] = [];

  data.forEach((point, index) => {
    // 验证必要字段
    if (!isValidMarkPoint(point)) {
      invalidPoints.push({ index, point });
      return;
    }

    // 添加到有效点列表
    validPoints.push(point);
  });

  // 显示验证结果
  if (invalidPoints.length > 0) {
    ElMessage.error(
      `发现 ${invalidPoints.length} 个无效的标记点数据，本次导入取消`,
    );
    console.warn("无效的标记点数据:", invalidPoints);
    return;
  }

  // 导入有效的标记点
  if (validPoints.length > 0) {
    importValidPoints(validPoints);
    ElMessage.success(`成功导入 ${validPoints.length} 个标记点`);
  } else {
    ElMessage.error("没有找到有效的标记点数据");
  }
};

// 验证标记点数据是否有效
const isValidMarkPoint = (point: any): boolean => {
  // 检查必要字段
  if (!point.name || typeof point.name !== "string") {
    return false;
  }

  // 验证位置信息
  if (
    !point.position ||
    typeof point.position.lon !== "number" ||
    typeof point.position.lat !== "number"
  ) {
    return false;
  }

  // 验证经纬度范围
  if (
    point.position.lon < -180 ||
    point.position.lon > 180 ||
    point.position.lat < -90 ||
    point.position.lat > 90
  ) {
    return false;
  }

  // 验证类型
  if (
    !point.type ||
    typeof point.type !== "string" ||
    !Object.values(MarkerTypeNameMap).includes(
      MarkerTypeNameMap[point.type as keyof typeof MarkerTypeNameMap],
    )
  ) {
    // 如果类型无效，设置为默认类型
    point.type = Object.keys(MarkerTypeNameMap)[0];
  }

  // 验证偏移量（如果存在）
  if (point.offset) {
    if (
      typeof point.offset.x !== "number" ||
      typeof point.offset.y !== "number"
    ) {
      // 如果偏移量无效，设置为默认值
      point.offset = { x: 0, y: 0 };
    }
  } else {
    // 如果没有偏移量，添加默认值
    point.offset = { x: 0, y: 0 };
  }

  // 确保高度值存在
  if (!point.position.alt || typeof point.position.alt !== "number") {
    point.position.alt = 0;
  }

  // 确保flag属性存在
  if (!point.flag) {
    point.flag = "imported"; // 默认设置为"imported"
  }

  // 确保chartType属性存在
  if (!point.chartType) {
    point.chartType = "default"; // 默认设置为"default"
  } else if (!["default", "pie", "bar"].includes(point.chartType)) {
    // 如果chartType不是有效值，设置为default
    point.chartType = "default";
  }

  return true;
};

// 导入有效的标记点
const importValidPoints = (points: Partial<MarkPoint>[]) => {
  const viewer = window.viewer;
  if (!viewer) {
    ElMessage.error("地图实例不存在，无法导入标记点");
    return;
  }

  // 导入每个标记点
  const importedPoints: MarkPoint[] = [];

  points.forEach((point) => {
    // 添加到存储，并标记为导入的数据
    const newPoint = markPointsStore.addMarkPoint({
      name: point.name!,
      position: point.position!,
      type: point.type!,
      flag: point.flag || "imported", // 添加flag属性，如果没有则设为"imported"
      offset: point.offset,
      description: point.description
        ? `[导入] ${point.description}`
        : "[导入数据]",
      visible: true, // 默认可见
      weeklyData: point.weeklyData || [],
      echartsData: point.echartsData || {},
      displayLevel: point.displayLevel,
      isImported: true, // 添加导入标记
      chartType: point.chartType || "default", // 添加图表类型，默认为default
    });

    importedPoints.push(newPoint);
  });

  // 在地图上显示导入的标记点
  checkAndRestoreMarkPoints();

  // 更新显示状态
  updateMarkersVisibility();

  // 如果有标记点被导入，调整视图以包含所有新导入的标记点
  if (importedPoints.length > 0) {
    // 创建一个矩形，包含所有新导入的标记点
    let west = 180;
    let south = 90;
    let east = -180;
    let north = -90;

    // 计算包含所有新导入标记点的矩形范围
    importedPoints.forEach((point) => {
      const lon = point.position.lon;
      const lat = point.position.lat;

      west = Math.min(west, lon);
      south = Math.min(south, lat);
      east = Math.max(east, lon);
      north = Math.max(north, lat);
    });

    // 添加一些边距
    const padding = 0.1;
    west -= padding;
    south -= padding;
    east += padding;
    north += padding;

    // 创建矩形
    const rectangle = Cesium.Rectangle.fromDegrees(west, south, east, north);

    // 飞行到矩形范围
    viewer.camera.flyTo({
      destination: rectangle,
      duration: 1.5,
    });
  }
};

// 打开图表选择弹窗
const openChartSelectionDialog = (point?: MarkPoint) => {
  // 如果传入了特定的标记点，可以在这里处理特定点的图表数据
  // 例如，可以将当前选中的点保存起来，以便在选择图表类型后使用
  if (point) {
    selectedPoint.value = point;
  }
  if (echartCard.value) {
    // 确保 echartsData 存在，如果不存在则传递空对象
    echartCard.value.open(point?.echartsData || {});
  }
};

defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.mark-point-dialog {
  .search-box {
    margin-bottom: 15px;

    .search-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;

      .search-input {
        flex: 1;
      }

      .button-group {
        display: flex;
        gap: 5px;
      }
    }
  }

  .upload-btn {
    display: inline-block;

    :deep(.el-upload) {
      display: inline-block;
    }
  }

  .point-list {
    height: 450px;
    overflow-y: auto;
  }

  .pagination-container {
    margin-top: 15px;
    display: flex;
    justify-content: center;
  }
}

.detail-item {
  margin-bottom: 15px;

  .label {
    font-weight: bold;
    margin-right: 5px;
  }

  .weekly-data {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    height: 120px;
    margin-top: 10px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 8px;

    .weekly-data-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 12%;

      .date {
        font-size: 12px;
        color: #606266;
        margin-bottom: 5px;
      }

      .value {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        min-height: 20px;
        background-color: #409eff;
        color: white;
        font-size: 12px;
        border-radius: 4px;
        transition: height 0.3s;
      }
    }
  }
}
</style>
