import * as Cesium from "cesium";

import { type Ref, ref } from "vue";

// 视角类型定义
export type ViewType = "nose" | "tail" | "free" | "top" | "third";

export interface ViewMode {
  type: ViewType;
  label: string;
  icon: string;
}

export interface ViewInfo {
  title: string;
  tips: string[];
}

export const useAircraftCamera = (
  viewer: Ref<Cesium.Viewer | null>,
  aircraftEntity: Ref<Cesium.Entity | null>,
) => {
  const currentView = ref<ViewType>("tail");
  let cameraUpdateListener: Cesium.Event.RemoveCallback | null = null;

  // 视角模式配置
  const viewModes: ViewMode[] = [
    { type: "nose", label: "机头", icon: "✈️" },
    { type: "tail", label: "机尾", icon: "🔙" },
    { type: "third", label: "第三人称", icon: "👁️" },
    { type: "top", label: "俯视", icon: "⬇️" },
    { type: "free", label: "自由", icon: "🎮" },
  ];

  // 视角信息配置
  const viewInfoMap: Record<ViewType, ViewInfo> = {
    nose: {
      title: "机头视角",
      tips: [
        "相机位于机头前方偏侧",
        "可看到飞机机身",
        "跟随飞机航向",
        "可拖拽调整角度",
      ],
    },
    tail: {
      title: "机尾视角",
      tips: [
        "相机位于机尾后方",
        "视角朝向飞机",
        "跟随飞机航向",
        "可拖拽旋转视角",
      ],
    },
    third: {
      title: "第三人称视角",
      tips: [
        "相机位于飞机侧后方",
        "最佳观察角度",
        "跟随飞机移动",
        "飞机始终可见",
      ],
    },
    free: {
      title: "自由视角",
      tips: [
        "相机完全自由移动",
        "初始朝向飞机",
        "可任意角度观察",
        "鼠标控制移动旋转",
      ],
    },
    top: {
      title: "俯视视角",
      tips: [
        "相机位于飞机正上方",
        "垂直俯视观察",
        "跟随飞机移动",
        "固定俯视角度",
      ],
    },
  };

  // 获取当前视角信息
  const getCurrentViewInfo = (): ViewInfo => {
    return viewInfoMap[currentView.value];
  };

  // 机头视角相机更新函数
  const updateNoseCamera = () => {
    if (!aircraftEntity.value || !viewer.value) return;

    const position = aircraftEntity.value.position?.getValue(
      viewer.value.clock.currentTime,
    );
    const orientation = aircraftEntity.value.orientation?.getValue(
      viewer.value.clock.currentTime,
    );

    if (position && orientation) {
      const hpr = Cesium.HeadingPitchRoll.fromQuaternion(orientation);

      // 相机位置在飞机前方偏右侧，确保能看到飞机机身
      const localOffset = new Cesium.Cartesian3(15, 25, 5); // 右侧15米，前方25米，上方5米

      const transform = Cesium.Transforms.headingPitchRollToFixedFrame(
        position,
        hpr,
      );
      const worldOffset = Cesium.Matrix4.multiplyByPoint(
        transform,
        localOffset,
        new Cesium.Cartesian3(),
      );

      // 计算朝向飞机的方向
      const direction = Cesium.Cartesian3.subtract(
        position,
        worldOffset,
        new Cesium.Cartesian3(),
      );
      Cesium.Cartesian3.normalize(direction, direction);

      // 计算正确的上方向向量
      const up = Cesium.Cartesian3.cross(
        direction,
        Cesium.Cartesian3.UNIT_Z,
        new Cesium.Cartesian3(),
      );
      Cesium.Cartesian3.cross(up, direction, up);
      Cesium.Cartesian3.normalize(up, up);

      // 设置相机位置和朝向，确保看向飞机
      viewer.value.scene.camera.position = worldOffset;
      viewer.value.scene.camera.direction = direction;
      viewer.value.scene.camera.up = up;
      viewer.value.scene.camera.right = Cesium.Cartesian3.cross(
        direction,
        up,
        new Cesium.Cartesian3(),
      );
    }
  };

  // 机尾视角相机更新函数
  const updateTailCamera = () => {
    if (!aircraftEntity.value || !viewer.value) return;

    const position = aircraftEntity.value.position?.getValue(
      viewer.value.clock.currentTime,
    );
    const orientation = aircraftEntity.value.orientation?.getValue(
      viewer.value.clock.currentTime,
    );

    if (position && orientation) {
      const hpr = Cesium.HeadingPitchRoll.fromQuaternion(orientation);

      // 相机位置在飞机后方，距离适中确保飞机完全可见
      const localOffset = new Cesium.Cartesian3(0, -25, 8); // 后方25米，上方8米

      const transform = Cesium.Transforms.headingPitchRollToFixedFrame(
        position,
        hpr,
      );
      const worldOffset = Cesium.Matrix4.multiplyByPoint(
        transform,
        localOffset,
        new Cesium.Cartesian3(),
      );

      // 计算朝向飞机的方向
      const direction = Cesium.Cartesian3.subtract(
        position,
        worldOffset,
        new Cesium.Cartesian3(),
      );
      Cesium.Cartesian3.normalize(direction, direction);

      // 设置相机位置和朝向，确保看向飞机
      viewer.value.scene.camera.position = worldOffset;
      viewer.value.scene.camera.direction = direction;
      viewer.value.scene.camera.up = Cesium.Cartesian3.UNIT_Z;
      viewer.value.scene.camera.right = Cesium.Cartesian3.cross(
        direction,
        viewer.value.scene.camera.up,
        new Cesium.Cartesian3(),
      );
    }
  };

  // 俯视视角相机更新函数
  const updateTopCamera = () => {
    if (!aircraftEntity.value || !viewer.value) return;

    const position = aircraftEntity.value.position?.getValue(
      viewer.value.clock.currentTime,
    );
    const orientation = aircraftEntity.value.orientation?.getValue(
      viewer.value.clock.currentTime,
    );

    if (position && orientation) {
      const hpr = Cesium.HeadingPitchRoll.fromQuaternion(orientation);

      // 相机位置在飞机正上方，距离适中确保飞机清晰可见
      const localOffset = new Cesium.Cartesian3(0, 0, 60); // 正上方60米

      const transform = Cesium.Transforms.headingPitchRollToFixedFrame(
        position,
        hpr,
      );
      const worldOffset = Cesium.Matrix4.multiplyByPoint(
        transform,
        localOffset,
        new Cesium.Cartesian3(),
      );

      // 计算向下的方向向量
      const direction = Cesium.Cartesian3.subtract(
        position,
        worldOffset,
        new Cesium.Cartesian3(),
      );
      Cesium.Cartesian3.normalize(direction, direction);

      // 计算正确的上方向向量（与飞机航向对齐）
      const aircraftForward = Cesium.Matrix3.multiplyByVector(
        Cesium.Matrix3.fromQuaternion(orientation),
        Cesium.Cartesian3.UNIT_Y,
        new Cesium.Cartesian3(),
      );

      // 设置相机位置和朝向，确保看向飞机
      viewer.value.scene.camera.position = worldOffset;
      viewer.value.scene.camera.direction = direction;
      viewer.value.scene.camera.up = aircraftForward;
      viewer.value.scene.camera.right = Cesium.Cartesian3.cross(
        direction,
        aircraftForward,
        new Cesium.Cartesian3(),
      );
    }
  };

  // 第三人称视角相机更新函数
  const updateThirdPersonCamera = () => {
    if (!aircraftEntity.value || !viewer.value) return;

    const position = aircraftEntity.value.position?.getValue(
      viewer.value.clock.currentTime,
    );
    const orientation = aircraftEntity.value.orientation?.getValue(
      viewer.value.clock.currentTime,
    );

    if (position && orientation) {
      const hpr = Cesium.HeadingPitchRoll.fromQuaternion(orientation);

      // 相机位置在飞机左后方，提供最佳的第三人称观察角度
      const localOffset = new Cesium.Cartesian3(-20, -30, 12); // 左侧20米，后方30米，上方12米

      const transform = Cesium.Transforms.headingPitchRollToFixedFrame(
        position,
        hpr,
      );
      const worldOffset = Cesium.Matrix4.multiplyByPoint(
        transform,
        localOffset,
        new Cesium.Cartesian3(),
      );

      // 计算朝向飞机的方向
      const direction = Cesium.Cartesian3.subtract(
        position,
        worldOffset,
        new Cesium.Cartesian3(),
      );
      Cesium.Cartesian3.normalize(direction, direction);

      // 设置相机位置和朝向，确保看向飞机
      viewer.value.scene.camera.position = worldOffset;
      viewer.value.scene.camera.direction = direction;
      viewer.value.scene.camera.up = Cesium.Cartesian3.UNIT_Z;
      viewer.value.scene.camera.right = Cesium.Cartesian3.cross(
        direction,
        viewer.value.scene.camera.up,
        new Cesium.Cartesian3(),
      );
    }
  };

  // 自由视角初始化函数
  const initFreeCamera = () => {
    if (!aircraftEntity.value || !viewer.value) return;

    const position = aircraftEntity.value.position?.getValue(
      viewer.value.clock.currentTime,
    );

    if (position) {
      // 设置相机到飞机附近的一个合适位置
      const offset = new Cesium.Cartesian3(50, -50, 30);
      const cameraPosition = Cesium.Cartesian3.add(
        position,
        offset,
        new Cesium.Cartesian3(),
      );

      // 计算朝向飞机的方向
      const direction = Cesium.Cartesian3.subtract(
        position,
        cameraPosition,
        new Cesium.Cartesian3(),
      );
      Cesium.Cartesian3.normalize(direction, direction);

      // 设置相机位置和朝向
      viewer.value.scene.camera.position = cameraPosition;
      viewer.value.scene.camera.direction = direction;
      viewer.value.scene.camera.up = Cesium.Cartesian3.UNIT_Z;
      viewer.value.scene.camera.right = Cesium.Cartesian3.cross(
        direction,
        viewer.value.scene.camera.up,
        new Cesium.Cartesian3(),
      );
    }
  };

  // 启用相机控制器
  const enableCameraControls = () => {
    if (!viewer.value) return;

    viewer.value.scene.screenSpaceCameraController.enableRotate = true;
    viewer.value.scene.screenSpaceCameraController.enableZoom = true;
    viewer.value.scene.screenSpaceCameraController.enableTilt = true;
    viewer.value.scene.screenSpaceCameraController.enableLook = true;

    // 设置缩放限制，防止相机距离过远或过近
    viewer.value.scene.screenSpaceCameraController.minimumZoomDistance = 10;
    viewer.value.scene.screenSpaceCameraController.maximumZoomDistance = 1000;
  };

  // 切换视角模式
  const switchCameraView = (viewType: ViewType) => {
    if (!viewer.value || !aircraftEntity.value) return;

    currentView.value = viewType;

    // 移除之前的监听器
    if (cameraUpdateListener) {
      cameraUpdateListener();
      cameraUpdateListener = null;
    }

    switch (viewType) {
      case "nose":
        viewer.value.trackedEntity = undefined;
        enableCameraControls();
        updateNoseCamera();
        cameraUpdateListener =
          viewer.value.clock.onTick.addEventListener(updateNoseCamera);
        break;

      case "tail":
        viewer.value.trackedEntity = undefined;
        enableCameraControls();
        updateTailCamera();
        cameraUpdateListener =
          viewer.value.clock.onTick.addEventListener(updateTailCamera);
        break;

      case "third":
        viewer.value.trackedEntity = undefined;
        enableCameraControls();
        updateThirdPersonCamera();
        cameraUpdateListener = viewer.value.clock.onTick.addEventListener(
          updateThirdPersonCamera,
        );
        break;

      case "top":
        viewer.value.trackedEntity = undefined;
        enableCameraControls();
        updateTopCamera();
        cameraUpdateListener =
          viewer.value.clock.onTick.addEventListener(updateTopCamera);
        break;

      case "free":
        viewer.value.trackedEntity = undefined;
        enableCameraControls();
        // 初始化自由视角，确保飞机可见
        initFreeCamera();
        break;

      default:
        // 默认使用第三人称视角
        switchCameraView("third");
        break;
    }
  };

  // 清理函数
  const cleanup = () => {
    if (cameraUpdateListener) {
      cameraUpdateListener();
      cameraUpdateListener = null;
    }
  };

  return {
    currentView,
    viewModes,
    getCurrentViewInfo,
    switchCameraView,
    cleanup,
  };
};
