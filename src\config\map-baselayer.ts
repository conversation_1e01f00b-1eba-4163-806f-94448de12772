import * as Cesium from "cesium";

import AmapMercatorTilingScheme from "@/components/tool/AmapMercatorTilingScheme";

/**
 * 封装创建影像底图图层的函数，避免全局挂载变量
 * @returns
 */
const createImageryLayers = () => {
  //全球影像底图
  const imgLayer = new Cesium.ImageryLayer(
    // {z}、{x}、{y}分别代表瓦片的级别、行号和列号，
    new Cesium.UrlTemplateImageryProvider({
      /**
       * 使用 {y}（适配 Cesium 专用瓦片服务）
       * 使用 {reverseY}（适配标准瓦片地图服务）
       */
      url: "http://192.168.1.129:86/Google/{z}/{x}/{reverseY}.png",
      credit: "img",
    })
  );
  //  销毁图层 imgLayer.destroy();
  // 影像注记
  const ciaLayer = new Cesium.ImageryLayer(
    new Cesium.UrlTemplateImageryProvider({
      url: "http://192.168.1.129:86/zhuji/{z}/{x}/{y}.png",
      credit: "cia",
      tilingScheme: new AmapMercatorTilingScheme(),
    })
  );
  // 矢量
  const vecLayer = new Cesium.ImageryLayer(
    new Cesium.UrlTemplateImageryProvider({
      url: "http://192.168.1.129:86/vec/{z}/{x}/{y}.jpg",
      credit: "vec",
    })
  );
  // 创建一个图像图层
  const esri = Cesium.ArcGisMapServerImageryProvider.fromUrl(
    "https://services.arcgisonline.com/ArcGIS/rest/services/World_Terrain_Base/MapServer"
  );
  // 加载高德地图
  const gaodeImageryProvider = new Cesium.ImageryLayer(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
      maximumLevel: 18,
      minimumLevel: 1,
      credit: "Amap",
    })
  );
  // 加载腾讯地图
  const tencentImageryProvider = new Cesium.ImageryLayer(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://p2.map.gtimg.com/sateTiles/{z}/{sx}/{sy}/{x}_{reverseY}.jpg?version=400",
      customTags: {
        // @ts-ignore
        sx: function (imageryProvider, x, y, level) {
          return x >> 4;
        },
        // @ts-ignore
        sy: function (imageryProvider, x, y, level) {
          return ((1 << level) - y) >> 4;
        },
      },
    })
  );
  // 加载ArcGIS地图服务
  const imageryProviderArcgis = new Cesium.ImageryLayer(
    new Cesium.WebMapTileServiceImageryProvider({
      url : 'https://services.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/WMTS',
      layer: 'World_Imagery',
      style: 'default',
      format: 'image/jpeg',
      tileMatrixSetID: 'GoogleMapsCompatible',
      maximumLevel: 19,
      // 属性 版权 https://www.esri.com/
      credit: new Cesium.Credit('© Esri', false)
    })
  );

  return { imgLayer, ciaLayer, vecLayer };
};

export { createImageryLayers };
