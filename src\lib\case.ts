import * as Cesium from "cesium";

import explotEffect from "~/src/components/yanshi/explotEffect";
import tool from "../components/tool/tool";

/**
 *
 * @description 案例集合
 */
class UseCase {
  private options: {
    viewerMap: Cesium.Viewer;
  };
  private timerId: any;
  private viewerMap: Cesium.Viewer;
  constructor(options: Record<string, any>) {
    this.viewerMap = options.viewerMap;
    this.timerId = null;
  }
  private CameraViewer(viewer: { camera: { setView: (arg0: { destination: Cesium.Cartesian3; }) => void; }; }, lon: number, lat: number, height: number | undefined) {
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(lon, lat, height)
  });
  }
  // 爆炸方法
  explode() {
    try {
      let effect = new explotEffect(this.viewerMap, {
        lon: 104.70601,
        lat: 36.040969,
      });
      this.timerId = setTimeout(() => {
        effect.remove();
        tool.reset();
        this.destroy();
      }, 6000);

      this.CameraViewer(this.viewerMap, 104.70601, 36.040969, 10000);
    } catch (error) {
      console.error("爆炸方法 => 执行过程中出现错误:", error);
      if (this.timerId) {
        clearTimeout(this.timerId);
      }
    }
  }

  // 坦克方法
  destroy() {
    if (this.timerId) {
      clearTimeout(this.timerId);
    }
  }
}
export { UseCase };
