import * as Cesium from "cesium";

// 基础坐标（中心点）
export const BaseCoordinates = {
  lon: 120.88707253245059,
  lat: 23.844790812408803,
  alt: 0,
};

// 红军单位坐标 - 增加单位之间的距离
export const RedForceUnits = [
  // 红军指挥部（上方）- 向北移动更远
  {
    id: "redHQ",
    name: "红军指挥中心",
    type: "red",
    position: {
      lon: 120.88707253245059,
      lat: 24.144790812408803, // 从24.044790812408803增加到24.144790812408803
      alt: 0,
    },
    model: {
      uri: "src/assets/models/TransportVehicle.glb",
      minimumPixelSize: 40,
      maximumScale: 500,
      color: Cesium.Color.RED,
    },
    label: true,
  },
  // 红军坦克营（中间偏左）- 向西移动更远
  {
    id: "redTankBattalion",
    name: "红军坦克营",
    type: "red",
    position: {
      lon: 120.68707253245059, // 从120.78707253245059减小到120.68707253245059
      lat: 23.854790812408803,
      alt: 0,
    },
    model: {
      uri: "src/assets/models/M1Tank.glb",
      minimumPixelSize: 300,
      maximumScale: 5000,
      color: Cesium.Color.RED,
    },
    label: true,
  },
  // 红军空军指挥部（右侧）- 向东移动更远
  {
    id: "redAirForceHQ",
    name: "红军空军指挥部",
    type: "red",
    position: {
      lon: 121.17707253245059, // 从121.07707253245059增加到121.17707253245059
      lat: 23.834790812408803,
      alt: 0,
    },
    model: {
      uri: "src/assets/models/TransportVehicle.glb",
      minimumPixelSize: 40,
      maximumScale: 500,
      color: Cesium.Color.RED,
    },
    label: true,
  },
  // 红军干扰雷达（下方）- 向南移动更远
  {
    id: "redJammingRadar",
    name: "红军干扰雷达",
    type: "red",
    position: {
      lon: 120.86707253245059,
      lat: 23.524790812408803, // 从23.624790812408803减小到23.524790812408803
      alt: 0,
    },
    model: {
      uri: "src/assets/models/leida.glb",
      minimumPixelSize: 40,
      maximumScale: 500,
      color: Cesium.Color.RED,
    },
    radarRange: 30000,
    label: true,
  },
];

// 蓝军飞机路径
export const BlueForceAircrafts = [
  // 蓝军飞机1
  {
    id: "blueAircraft1",
    name: "蓝军战斗机1",
    type: "blue",
    model: {
      // uri: "src/assets/models/F14-warplane.glb",
      uri: "src/assets/models/plan1.glb",
      minimumPixelSize: 150, // 从150减小到50
      maximumScale: 2000, // 从2000减小到500
      // color: Cesium.Color.BLUE,
      distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
        0,
        10000000,
      ),
    },
    // 飞行路径保持不变
    coordinates: [
      {
        lon: 120.45437834723875, // 更远的起始点
        lat: 22.308069692936536, // 更远的起始点
        alt: 10000,
      },
      {
        lon: 120.27001538021575, // 调整坐标
        lat: 22.504264080922906,
        alt: 10000,
      },
      {
        lon: 120.16879955991711, // 调整坐标
        lat: 22.710914531487427,
        alt: 10000,
      },
      // 进入干扰区域
      {
        lon: 120.11746697166725, // 调整坐标
        lat: 23.133143219640053, // 增加距离
        alt: 10000,
      },
      {
        lon: 120.04816000064421, // 调整坐标
        lat: 23.504885313361376, // 增加距离
        alt: 10000,
      },
      {
        lon: 120.33032316909888, // 调整坐标，增加距离
        lat: 23.815287974843445,
        alt: 10000,
      },
      // 干扰区域中心
      {
        lon: 120.86707253245059,
        lat: 23.824790812408803,
        alt: 10000,
      },
      // 离开干扰区域
      {
        lon: 120.58135415011987, // 调整坐标，增加距离
        lat: 24.08375973224393, // 增加距离
        alt: 10000,
      },
      {
        lon: 120.66655147254645, // 调整坐标
        lat: 23.992735709339473, // 增加距离
        alt: 10000,
      },
      {
        lon: 120.88707253245059,
        lat: 23.844790812408803,
        alt: 10000,
      },
    ],
  },
  // 蓝军飞机2
  {
    id: "blueAircraft2",
    name: "蓝军战斗机2",
    type: "blue",
    model: {
      // uri: "src/assets/models/F14-warplane.glb",
      uri: "src/assets/models/plan1.glb",
      minimumPixelSize: 150, // 从150减小到50
      maximumScale: 2000, // 从2000减小到500
      // color: Cesium.Color.BLUE,
      distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
        0,
        10000000,
      ),
    },
    // 飞行路径保持不变
    coordinates: [
      {
        lon: 121.5294554652585, // 更远的起始点
        lat: 23.69532868533545, // 增加距离
        alt: 8000,
      },
      {
        lon: 121.49907352499065, // 调整坐标
        lat: 23.28540001026689, // 增加距离
        alt: 8000,
      },
      {
        lon: 121.43475217216782, // 调整坐标
        lat: 23.11236641411644, // 增加距离
        alt: 8000,
      },
      // 进入干扰区域
      {
        lon: 121.27004577307091, // 调整坐标，增加距离
        lat: 22.766514735195498, // 增加距离
        alt: 8000,
      },
      {
        lon: 121.14605612267153, // 调整坐标，增加距离
        lat: 22.60821278942727, // 增加距离
        alt: 8000,
      },
      // 干扰区域中心
      {
        lon: 120.86707253245059,
        lat: 23.824790812408803,
        alt: 8000,
      },
      // 离开干扰区域
      {
        lon: 120.72932444970317, // 调整坐标，增加距离
        lat: 23.518708686883302, // 增加距离
        alt: 8000,
      },
      {
        lon: 120.88707253245059,
        lat: 23.844790812408803,
        alt: 8000,
      },
    ],
  },
];
