// @ts-nocheck
const BD_FACTOR = (3.14159265358979324 * 3000.0) / 180.0; // 百度坐标系的修正因子
const PI = 3.1415926535897932384626; // 圆周率
const RADIUS = 6378245.0; // 地球半径
const EE = 0.00669342162296594323; // 椭球的偏心率平方

class CoordTransform {
  /**
   * BD-09(百度坐标系) To GCJ-02(火星坐标系) 的转换方法
   * @param lon 经度
   * @param lat 纬度
   * @returns {number[]} 返回经纬度数组
   */
  static BD09ToGCJ02(lon, lat) {
    let x = +lon - 0.0065; // 经度修正
    let y = +lat - 0.006; // 纬度修正
    let z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * BD_FACTOR); // z 值修正
    let theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * BD_FACTOR); // theta 值修正
    let gg_lon = z * Math.cos(theta); // 修正后的经度
    let gg_lat = z * Math.sin(theta); // 修正后的纬度
    return [gg_lon, gg_lat];
  }

  /**
   * GCJ-02(火星坐标系) To BD-09(百度坐标系) 的转换方法
   * @param lon 经度
   * @param lat 纬度
   * @returns {number[]} 返回经纬度数组
   */
  static GCJ02ToBD09(lon, lat) {
    lat = +lat;
    lon = +lon;
    let z =
      Math.sqrt(lon * lon + lat * lat) + 0.00002 * Math.sin(lat * BD_FACTOR); // z 值修正
    let theta = Math.atan2(lat, lon) + 0.000003 * Math.cos(lon * BD_FACTOR); // theta 值修正
    let bd_lon = z * Math.cos(theta) + 0.0065; // 修正后的经度
    let bd_lat = z * Math.sin(theta) + 0.006; // 修正后的纬度
    return [bd_lon, bd_lat];
  }

  /**
   * WGS-84(世界大地坐标系) To GCJ-02(火星坐标系) 的转换方法
   * @param lon 经度
   * @param lat 纬度
   * @returns {number[]} 返回经纬度数组
   */
  static WGS84ToGCJ02(lon, lat) {
    lat = +lat;
    lon = +lon;
    if (this.out_of_china(lon, lat)) {
      // 如果在中国境内
      return [lon, lat];
    } else {
      let d = this.delta(lon, lat); // 计算经纬度偏移值
      return [lon + d[0], lat + d[1]]; // 返回修正后的经纬度
    }
  }

  /**
   * GCJ-02(火星坐标系) To WGS-84(世界大地坐标系) 的转换方法
   * @param lon 经度
   * @param lat 纬度
   * @returns {number[]} 返回经纬度数组
   */
  static GCJ02ToWGS84(lon, lat) {
    lat = +lat;
    lon = +lon;
    if (this.out_of_china(lon, lat)) {
      // 如果在中国境内
      return [lon, lat];
    } else {
      let d = this.delta(lon, lat); // 计算经纬度偏移值
      let mgLon = lon + d[0];
      let mgLat = lat + d[1];
      return [lon * 2 - mgLon, lat * 2 - mgLat]; // 返回修正后的经纬度
    }
  }

  /**
   * 计算经纬度偏移值的方法
   * @param lon 经度
   * @param lat 纬度
   * @returns {number[]} 返回经纬度偏移值数组
   */
  static delta(lon, lat) {
    let dLon = this.transformLon(lon - 105, lat - 35); // 经度偏移值
    let dLat = this.transformLat(lon - 105, lat - 35); // 纬度偏移值
    const radLat = (lat / 180) * PI;
    let magic = Math.sin(radLat);
    magic = 1 - EE * magic * magic;
    const sqrtMagic = Math.sqrt(magic);
    dLon = (dLon * 180) / ((RADIUS / sqrtMagic) * Math.cos(radLat) * PI);
    dLat = (dLat * 180) / (((RADIUS * (1 - EE)) / (magic * sqrtMagic)) * PI);
    return [dLon, dLat];
  }
  static transformLon(lon, lat) {
    lat = +lat;
    lon = +lon;
    let ret =
      300.0 +
      lon +
      2.0 * lat +
      0.1 * lon * lon +
      0.1 * lon * lat +
      0.1 * Math.sqrt(Math.abs(lon));
    ret +=
      ((20.0 * Math.sin(6.0 * lon * PI) + 20.0 * Math.sin(2.0 * lon * PI)) *
        2.0) /
      3.0;
    ret +=
      ((20.0 * Math.sin(lon * PI) + 40.0 * Math.sin((lon / 3.0) * PI)) * 2.0) /
      3.0;
    ret +=
      ((150.0 * Math.sin((lon / 12.0) * PI) +
        300.0 * Math.sin((lon / 30.0) * PI)) *
        2.0) /
      3.0;
    return ret;
  }

  /**
   *
   * @param lon
   * @param lat
   * @returns {number}
   */
  static transformLat(lon, lat) {
    lat = +lat;
    lon = +lon;
    let ret =
      -100.0 +
      2.0 * lon +
      3.0 * lat +
      0.2 * lat * lat +
      0.1 * lon * lat +
      0.2 * Math.sqrt(Math.abs(lon));
    ret +=
      ((20.0 * Math.sin(6.0 * lon * PI) + 20.0 * Math.sin(2.0 * lon * PI)) *
        2.0) /
      3.0;
    ret +=
      ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) * 2.0) /
      3.0;
    ret +=
      ((160.0 * Math.sin((lat / 12.0) * PI) +
        320 * Math.sin((lat * PI) / 30.0)) *
        2.0) /
      3.0;
    return ret;
  }

  /**
   * 判断是否在国内。不在国内不做偏移
   * @param lon
   * @param lat
   * @returns {boolean}
   */
  static out_of_china(lon, lat) {
    lat = +lat;
    lon = +lon;
    return !(lon > 73.66 && lon < 135.05 && lat > 3.86 && lat < 53.55);
  }
}
export default CoordTransform;
