<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"
    type="image/x-icon" />
  <link rel="icon" type="image/svg+xml"
    href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" />
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="./demo.css">
  <link rel="stylesheet" href="./iconfont.css">
  <script src="./iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>

<body>
  <div class="main">
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Font class</span></li>
      </ul>

    </div>
    <div class="tab-container">
      <div class="content font-class" style="display: block;">
        <ul class="icon_lists dib-box" id="fontShow">

          <li class="dib">
            <span class="icon iconfont gis-tanke"></span>
            <div class="name">
            </div>
            <div class="code-name">.gis-tanke
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <script>
      const iconFontArr = ["gis-tanke","gis-daodanzhuangjiache","gis-u6938","gis-mubiao","gis-icon_mubiao","gis-xiaoshoumubiao","gis-icon_fanghumubiao","gis-wancheng-01","gis-quxiao","gis-lujingguihua","gis-lujingguihua1","gis-lujingguihua2","gis-zhandouji1","gis-zhandouji2","gis-ditutaishi","gis-zhongxinkaishi","gis-zanting","gis-anniu-jiantouxiangyou","gis-anniu_jiantouxiangzuo","gis-jiantou_yemian_xiangshang","gis-jiantou_yemian_xiangxia","gis-jiantou_yemian_xiangyou","gis-jiantou_yemian_xiangzuo","gis-icon-test","gis-lunshizhuangjiache","gis-xiangxingfuhao-35-tanke","gis-junjian","gis-zhandouji","gis-warship-2fuben","gis-a-zhandoujizuozhan","gis-bianji","gis-bianji1","gis-jiesuo","gis-suoding","gis-bofang","gis-huojianjiasu","gis-tingzhi","gis-anniu_guanbi","gis-bangzhu","gis-shouye","gis-guanyu","gis-jiazai_shuaxin","gis-jinggao","gis-lishijilu","gis-shanchu2","gis-quanjushezhi","gis-shuaxin","gis-tishi","gis-sousuo","gis-jiantou_shangxiaqiehuan","gis-jiantou_zuoyouqiehuan","gis-quxiaoquanping","gis-quanping","gis-dingwei1","gis-yuan","gis-juxing1","gis-shanxingsousuo","gis-jiandan","gis-zhexianjiantou","gis-beisaierquxiandaijiantou","gis-xian","gis-quyu","gis-yingjibiaohui","gis-shipinbiaohui","gis-taishibiaohui","gis-tubiaohuizhi_jiaru","gis-biaoji","gis-biaohui-shuangjiantou","gis-biaohui","gis-biaohui-shuangjiantou1","gis-jiantoubiaohui","gis-sanweibiaohui","gis-a-taishibiaohui--jingongfangxiangwei","gis-guanbi","gis-fangweijiao","gis-shuipingceju","gis-gongjuxiang","gis-jurassic_toolkit1","gis-gongjuxiang1","gis-changjing","gis-dingwei","gis-shangchuan","gis-zhuye","gis-jurassic_toolkit","gis-jiandanmoxing","gis-junbiaobiaohui","gis-lianxi2hebing-16","gis-a-icon-gongjuxiangxian","gis-chexiao","gis-huifu","gis-fuzhi","gis-shanchu1","gis-huaxian","gis-juxing","gis-huizhimian","gis-yingxiang","gis-yingxiang1","gis-shiliang","gis-out","gis-measure-tool","gis-in","gis-guiji","gis-a-3d","gis-mti-gong-quanping","gis-mti-gong-tuichuquanping","gis-erwei","gis-measureTool","gis-tuceng","gis-texiao","gis-leida","gis-snow","gis-rain","gis-suoxiao","gis-ceju","gis-chushi","gis-shanchu","gis-icon--shanchu","gis-fangda","gis-zhongzhi","gis-mianji"]

    $(document).ready(function () {

      let liContent= ""
     
      // 动态生成 <li> 元素
      iconFontArr.forEach((iconClass) => {
        liContent += `
          <li class="dib">
            <span class="icon iconfont ${iconClass}"></span>
            <div class="name"></div>
            <div class="code-name">.${iconClass}</div>
          </li>
        `;
      });

      $("#fontShow").html(liContent);
    })
  </script>
</body>

</html>
