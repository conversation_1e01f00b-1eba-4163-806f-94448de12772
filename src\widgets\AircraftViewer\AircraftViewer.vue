<template>
  <div class="mainContainer">
    <div id="cesium-viewer" ref="viewerContainer"></div>
    <!-- 视角切换控制面板 -->
    <div class="camera-controls">
      <div class="view-selector">
        <button
          v-for="view in viewModes"
          :key="view.type"
          @click="switchCameraView(view.type)"
          :class="['view-btn', { active: currentView === view.type }]"
        >
          <span class="view-icon">{{ view.icon }}</span>
          <span class="view-label">{{ view.label }}</span>
        </button>
      </div>
      <button @click="resetAnimation" class="reset-btn">重置动画</button>
      <div v-if="currentView !== 'free'" class="view-tips">
        <small class="tips-title">{{ getCurrentViewInfo().title }}：</small>
        <small v-for="tip in getCurrentViewInfo().tips" :key="tip"
          >• {{ tip }}</small
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";

import { onBeforeUnmount, onMounted, ref } from "vue";

import { useAircraftCamera } from "@/hooks/useAircraftCamera";

import { initMap } from "@/utils/initMap";

const viewerContainer = ref<HTMLElement | null>(null);
const viewer = ref<Cesium.Viewer | null>(null);
const planURL = "/src/assets/models/plan1.glb";

const aircraftEntity = ref<Cesium.Entity | null>(null);
let animationInterval: number | null = null;

// 使用视角控制 hook
const {
  currentView,
  viewModes,
  getCurrentViewInfo,
  switchCameraView,
  cleanup: cleanupCamera,
} = useAircraftCamera(viewer, aircraftEntity);
// 在北京天安门上空创建飞机模型，设置朝向
// createModel(
//   viewer,
//   planURL,
//   2000, // 高度2000米
//   116.4074, // 北京经度
//   39.9042, // 北京纬度
//   225, // 朝向角度，使飞机朝向东南方
//   10, // 50, // 机头略微向上
//   0, // 0, // 不倾斜
// );

const initViewer = async () => {
  viewer.value = await initMap(viewerContainer);
  viewer.value.entities.removeAll();

  const startLon = 116.3975; // 天安门
  const startLat = 39.9087;
  const endLon = 116.3269; // 清华大学
  const endLat = 40.0032;

  // 定义飞行关键高度点：起飞 -> 上升 -> 巡航 -> 下降
  // const heights = [100, 800, 1500, 2000, 2000, 2000, 2000, 2000];
  const heights = [2000, 2000, 20000, 2000, 2000, 2000, 2000, 2000];
  const pointCount = heights.length;
  const secondsBetweenPoints = 3;

  const start = Cesium.JulianDate.now();
  const position = new Cesium.SampledPositionProperty();

  // 设置插值算法为 Hermite，多点之间平滑曲线
  position.setInterpolationOptions({
    interpolationDegree: 5,
    interpolationAlgorithm: Cesium.HermitePolynomialApproximation,
  });

  // for (let i = 0; i < pointCount; i++) {
  //   const lon = startLon + ((endLon - startLon) * i) / (pointCount - 1);
  //   const lat = startLat + ((endLat - startLat) * i) / (pointCount - 1);
  //   const height = heights[i];

  //   const time = Cesium.JulianDate.addSeconds(
  //     start,
  //     i * secondsBetweenPoints,
  //     new Cesium.JulianDate(),
  //   );
  //   const positionSample = Cesium.Cartesian3.fromDegrees(lon, lat, height);
  //   position.addSample(time, positionSample);
  // }

  // const stop = Cesium.JulianDate.addSeconds(
  //   start,
  //   (pointCount - 1) * secondsBetweenPoints,
  //   new Cesium.JulianDate(),
  // );

  aircraftEntity.value = viewer.value.entities.add({
    availability: new Cesium.TimeIntervalCollection([
      new Cesium.TimeInterval({ start, stop }),
    ]),
    position,
    orientation: new Cesium.VelocityOrientationProperty(position),
    model: {
      uri: planURL,
      minimumPixelSize: 32, // 确保飞机在远距离时仍然可见
      maximumScale: 50, // 允许更大的缩放
      scale: 0.1, // 增大飞机模型尺寸
      // silhouetteColor: Cesium.Color.YELLOW, // 添加轮廓线提高可见性
      // silhouetteSize: 2,
    },
  });

  viewer.value.clock.startTime = start.clone();
  viewer.value.clock.stopTime = stop.clone();
  viewer.value.clock.currentTime = start.clone();
  viewer.value.clock.clockRange = Cesium.ClockRange.CLAMPED;
  viewer.value.clock.multiplier = 1;
  viewer.value.clock.shouldAnimate = true;

  // 设置默认视角为第三人称视角
  setTimeout(() => {
    switchCameraView("third");
  }, 100);

  // 设置初始相机视角
  // viewer.camera.setView({
  //   destination: Cesium.Cartesian3.fromDegrees(116.45, 39.9042, 5000),
  //   orientation: {
  //     heading: Cesium.Math.toRadians(90),
  //     pitch: Cesium.Math.toRadians(-45),
  //     roll: 0.0,
  //   },
  // });
};

// 重置动画
const resetAnimation = () => {
  if (!viewer.value) return;

  viewer.value.clock.currentTime = viewer.value.clock.startTime.clone();
  viewer.value.clock.shouldAnimate = true;

  // 重新设置当前视角
  switchCameraView(currentView.value);
};

onMounted(() => {
  initViewer();
});

onBeforeUnmount(() => {
  cleanupCamera();
  if (viewer.value) {
    viewer.value.destroy();
  }
  if (animationInterval) {
    clearInterval(animationInterval);
  }
});
</script>

<style scoped>
.mainContainer {
  position: relative;
  width: 100%;
  height: 100vh;
}

#cesium-viewer {
  width: 100%;
  height: 100%;
}

.camera-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 1000;
}

.view-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  min-width: 220px;
}

.view-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  border: none;
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.85);
  color: rgba(148, 163, 184, 0.9);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(30, 41, 59, 0.5);
  position: relative;
  overflow: hidden;
}

.view-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.view-btn:hover::before {
  left: 100%;
}

.view-btn:hover {
  background: rgba(30, 41, 59, 0.9);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  color: rgba(226, 232, 240, 1);
}

.view-btn.active {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.2),
    rgba(37, 99, 235, 0.3)
  );
  border-color: rgba(59, 130, 246, 0.6);
  color: rgba(147, 197, 253, 1);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.view-btn.active:hover {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.3),
    rgba(37, 99, 235, 0.4)
  );
  transform: translateY(-2px);
}

.view-icon {
  font-size: 18px;
  filter: drop-shadow(0 0 4px currentColor);
}

.view-label {
  font-weight: 500;
  letter-spacing: 0.5px;
}

.reset-btn {
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.85);
  color: rgba(148, 163, 184, 0.9);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(30, 41, 59, 0.5);
  letter-spacing: 0.5px;
}

.reset-btn:hover {
  background: rgba(30, 41, 59, 0.9);
  border-color: rgba(34, 197, 94, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  color: rgba(226, 232, 240, 1);
}

.view-tips {
  background: rgba(15, 23, 42, 0.9);
  border-radius: 8px;
  padding: 12px 16px;
  border: 1px solid rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(12px);
  max-width: 220px;
}

.view-tips small {
  display: block;
  color: rgba(148, 163, 184, 0.8);
  font-size: 11px;
  line-height: 1.5;
  margin: 3px 0;
}

.tips-title {
  color: rgba(147, 197, 253, 1) !important;
  font-weight: 600 !important;
  margin-bottom: 6px !important;
  font-size: 12px !important;
  text-shadow: 0 0 8px rgba(147, 197, 253, 0.3);
}
</style>
