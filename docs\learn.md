## obj -> gltf/gltb

```bash
obj2gltf -i ./src/assets/models/Building.obj -o ./src/assets/models/build.glb ## cli
# glb to b3dm
npx 3d-tiles-tools -i ./src/assets/models/build.glb -o ./src/assets/models/build.b3dm
## generate json
3d-tiles-tools createTilesetJson -i model.b3dm --output tileset.json
```


# Cesium加载影像图层
- 在Cesium中，加载影像图层主要通过 ImageryLayer ImageryProvider ImageryLayerCollection 三个类

## ImageryLayer
`
  var imageryLayer = new Cesium.ImageryLayer(imageryProvider, options);

  在Cesium中，使用ImageryLayer对象来表示一个影像图层。ImageryLayer是一个包含一个或多个瓦片的图层，它可以用来控制地图影像的显示、叠加和透明度等属性。可以通过将其添加到ImageryLayerCollection中来实现在场景中显示。
  常用属性
    imageryProvider：一个ImageryProvider对象，用于提供地图影像数据
    alpha：影像图层的透明度（0-1），默认值为1
    brightness：影像图层的亮度调整值（-1到1之间），默认值为0
    contrast：影像图层的对比度调整值（-1到1之间），默认值为0
    hue：影像图层的色调调整值（-1到1之间），默认值为0
    saturation：影像图层的饱和度调整值（-1到1之间），默认值为0
    gamma：影像图层的伽马调整值（>=1），默认值为1
    show：布尔类型，表示该图层是否可见，默认值为true
    minimumTerrainLevel：数字类型，表示在地形高程数据缺失时，该图层的最小可见级别。默认值为0
    maximumTerrainLevel：数字类型，表示在地形高程数据缺失时，该图层的最大可见级别。默认值为Infinity
    rectangle：一个Rectangle对象，表示该图层的可视范围
    zIndex：数字类型，表示该图层在图层堆叠顺序中的位置，数值越大表示越靠前
  常用方法
    destroy()：销毁该图层，释放资源。
    该方法会销毁该图层对象，释放其占用的资源。当不再需要该图层时，应该调用它来避免内存泄漏。
`
## ImageryProvider

`
  ImageryProvider 是Cesium中提供影像数据的抽象类，定义了一些基本方法和属性，用于获取、处理和显示影像数据。
  在实际应用中，通常需要根据不同的影像数据源选择不同的 ImageryProvider 子类，
  如 WebMapServiceImageryProvider、WebMapTileServiceImageryProvider、BingMapsImageryProvider等。
`
## ImageryLayerCollection
`
  ImageryLayerCollection 类是Cesium中的一个类，用于存储和管理多个 ImageryLayer 对象。
  每个 ImageryLayer 对象表示一个图像图层，可以包含单张图片或图像切片集合。
  常用属性
    length：返回集合中ImageryLayer对象的数量
    layerAdded：当图像图层添加到该集合时发生的事件
    layerMoved：当图像图层在该集合内移动时发生的事件
    layerRemoved：当图像图层从该集合中删除时发生的事件
  常用方法
    add(layer, index)：将给定的ImageryLayer对象添加到集合中的指定位置。如果省略索引，则将该图像图层添加到末尾
    remove(layer)：从集合中删除给定的ImageryLayer对象
    removeAll()：从集合中删除所有ImageryLayer对象
    raise(layer)：将给定的ImageryLayer对象移动到集合中的下一个位置
    lower(layer)：将给定的ImageryLayer对象移动到集合中的上一个位置
    raiseToTop(layer)：将给定的ImageryLayer对象移动到集合的顶部
    lowerToBottom(layer)：将给定的ImageryLayer对象移动到集合的底部
    addImageryProvider(imageryProvider, index)：用于将给定的ImageryProvider对象创建的图像图层添加到集合中的指定位置。如果省略索引，则将该图像图层添加到末尾
`