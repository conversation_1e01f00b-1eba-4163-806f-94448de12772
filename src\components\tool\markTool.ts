/*
 * @Author: <PERSON>
 * @Date: 2024-10-29 14:06:28
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-11-12 09:23:52
 * @FilePath: \mads-gis\src\components\tool\markTool.ts
 * @Description: 标记工具
 */
import * as <PERSON>sium from "cesium";
import pointHadler from "~/src/components/point/pointHadler";
import tool from "~/src/components/tool/tool";
import { v4 as uuid4 } from "uuid";
import { globalData } from "~/src/data/data";

class markTool {
  static markPoint() {
    const viewer: Cesium.Viewer = window.viewer;
    tool.cesiumCursorConvert("pointer"); // 修改鼠标手势
    if (viewer) {
      let handlers = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
      handlers.setInputAction(function (movement: any) {
        var cartesian = tool.getCatesian3FromPX(movement.position);
        pointHadler.addOneIconPoint({ position: cartesian, name: "123456" });
        console.log("监听左键", movement.position);
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      handlers.setInputAction(function (movement: { endPosition: any }) {
        var cartesian = tool.getCatesian3FromPX(movement.endPosition);
        const obj = {
          position: cartesian,
          text: "左键选点，右键结束",
        };
        tool.mouseTooltip(obj);
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      handlers.setInputAction(function (movement: any) {
        var cartesian = tool.getCatesian3FromPX(movement.position);
        handlers.destroy();
        tool.cesiumCursorConvert("default"); // 修改鼠标手势
        tool.mouseTooltip({}, true);
      }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }
  }

  static markPolyline() {
    const viewer = window.viewer,
      ids = new Date().getTime(),
      drawLayer = new Cesium.CustomDataSource(ids.toString());
    viewer.dataSources.add(drawLayer);
    tool.cesiumCursorConvert("pointer"); // 修改鼠标手势
    if (viewer) {
      let positions: any[] = [],
        lineEntity = new Cesium.Entity(),
        lineObj: Cesium.Entity,
        handlers = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
      handlers.setInputAction(function (movement: { position: any }) {
        var cartesian = tool.getCatesian3FromPX(movement.position);
        if (cartesian && cartesian.x) {
          if (positions.length == 0) {
            positions.push(cartesian.clone());
          }
          positions.push(cartesian);
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      handlers.setInputAction(function (movement: { endPosition: any }) {
        var cartesian = tool.getCatesian3FromPX(movement.endPosition);
        const obj = {
          position: cartesian,
          text: "左键绘点，右键结束",
        };
        tool.mouseTooltip(obj);
        if (positions.length >= 2) {
          if (cartesian && cartesian.x) {
            positions.pop();
            positions.push(cartesian);
          }
        }
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      handlers.setInputAction(function (movement: { position: any }) {
        handlers.destroy();
        let cartesian = tool.getCatesian3FromPX(movement.position);
        tool.cesiumCursorConvert("default");
        tool.mouseTooltip({}, true);
      }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

      lineEntity.name = ids.toString();
      lineEntity.polyline = {
        width: 3,
        material: Cesium.Color.fromCssColorString("#59F9FF"),
        clampToGround: false,
      };
      lineEntity.polyline.positions = new Cesium.CallbackProperty(function () {
        return positions;
      }, false);

      lineObj = drawLayer.entities.add(lineEntity);
    }
  }

  static markPlanePolygon() {
    const viewer = window.viewer,
      ids = new Date().getTime(),
      drawLayer = new Cesium.CustomDataSource(ids.toString());
    viewer.dataSources.add(drawLayer);
    tool.cesiumCursorConvert("pointer"); // 修改鼠标手势
    if (viewer) {
      var positions: any[] = [],
        polygon = new Cesium.PolygonHierarchy(),
        polygonEntity = new Cesium.Entity(),
        polyObj: null = null,
        handlers = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
      handlers.setInputAction(function (movement: { position: any }) {
        var cartesian = tool.getCatesian3FromPX(movement.position);
        if (cartesian && cartesian.x) {
          if (positions.length == 0) {
            polygon.positions.push(cartesian.clone());
            positions.push(cartesian.clone());
          }
          positions.push(cartesian.clone());
          polygon.positions.push(cartesian.clone());

          if (!polyObj) create();
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      handlers.setInputAction(function (movement: { position: any }) {
        var cartesian = tool.getCatesian3FromPX(movement.position);
        const obj = {
          position: cartesian,
          text: "左键绘点，右键结束",
        };
        tool.mouseTooltip(obj);
        if (positions.length >= 2) {
          if (cartesian && cartesian.x) {
            positions.pop();
            positions.push(cartesian);
            polygon.positions.pop();
            polygon.positions.push(cartesian);
          }
        }
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      handlers.setInputAction(function (movement: { endPosition: any }) {
        let cartesian = tool.getCatesian3FromPX(movement.endPosition);
        handlers.destroy();
        positions.push(positions[0]);
        let obj = {
          id: ids,
          position: cartesian,
          entity: drawLayer,
        };
        pointHadler.addMeasureInfoPoint(obj, true);
        tool.cesiumCursorConvert("default");
        tool.mouseTooltip({}, true);
      }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

      function create() {
        polygonEntity.name = ids;
        polygonEntity.polyline = {
          width: 3,
          material: Cesium.Color.fromCssColorString("#59FF9B"),
          clampToGround: false,
        };

        polygonEntity.polyline.positions = new Cesium.CallbackProperty(
          function () {
            return positions;
          },
          false
        );

        polygonEntity.polygon = {
          hierarchy: new Cesium.CallbackProperty(function () {
            return polygon;
          }, false),
          material: Cesium.Color.WHITE.withAlpha(0.1),
          clampToGround: false,
        };

        polyObj = drawLayer.entities.add(polygonEntity);
      }
    }
  }

  static markPlaneCircle() {
    const viewer = window.viewer,
      ids = new Date().getTime(),
      drawLayer = new Cesium.CustomDataSource("drawcircle" + ids.toString());
    viewer.dataSources.add(drawLayer);
    let handlers = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas),
      positions: Cesium.Cartesian3[] = [];
    tool.cesiumCursorConvert("pointer"); // 修改鼠标手势
    let circleEntity = new Cesium.Entity({
      id: "drawcircle" + uuid4(),
      point: {
        color: Cesium.Color.fromCssColorString("#CDDC39"),
        pixelSize: 6,
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
          0,
          3000000
        ),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      },
      ellipse: {
        height: 0,
        outline: true,
        outlineWidth: 15,
        material: Cesium.Color.WHITE.withAlpha(0.2),
        outlineColor: Cesium.Color.fromCssColorString("#59FF9B"),
        // distanceDisplayCondition:new Cesium.DistanceDisplayCondition(10,3000000),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      },
    });
    handlers.setInputAction(function (movement: { position: any }) {
      var cartesian = tool.getCatesian3FromPX(movement.position);
      if (cartesian && cartesian.x) {
        if (positions.length == 0) {
          circleEntity.position = cartesian;
          positions.push(cartesian);
        } else {
          handlers.destroy();
          tool.mouseTooltip({}, true);
          tool.cesiumCursorConvert("default"); // 修改鼠标手势
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    handlers.setInputAction(function (movement: { endPosition: any }) {
      var cartesian = tool.getCatesian3FromPX(movement.endPosition);
      const obj = {
        position: cartesian,
        text: "左键选点 结束绘制",
      };
      tool.mouseTooltip(obj);
      if (positions.length > 0) {
        let radius = Cesium.Cartesian3.distance(positions[0], cartesian);
        circleEntity.ellipse.semiMajorAxis = new Cesium.CallbackProperty(
          function () {
            return radius;
          },
          false
        );
        circleEntity.ellipse.semiMinorAxis = new Cesium.CallbackProperty(
          function () {
            return radius;
          },
          false
        );
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    drawLayer.entities.add(circleEntity);
  }

  static markPlaneRectangle() {
    const viewer = window.viewer,
      ids = new Date().getTime(),
      drawLayer = new Cesium.CustomDataSource(
        "drawPlaneRectangle" + ids.toString()
      ),
      scene = viewer.scene;
    viewer.dataSources.add(drawLayer);
    let handlers = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas),
      firstPoint: { lon: any; lat: any },
      secondPoint: { lon: any; lat: any };
    tool.cesiumCursorConvert("pointer"); // 修改鼠标手势
    let rectangleEntity = new Cesium.Entity({
      id: "drawRectangle" + uuid4(),
      rectangle: {
        height: 0,
        fill: true,
        outline: true,
        outlineWidth: 15,
        outlineColor: Cesium.Color.fromCssColorString("#59FF9B"),
        material: Cesium.Color.WHITE.withAlpha(0.4),
      },
    });
    handlers.setInputAction(function (movement: { position: any }) {
      const ray = viewer.camera.getPickRay(movement.position);
      const ellipsoid = scene.globe.ellipsoid;
      const intersection = scene.globe.pick(ray, scene);

      if (intersection) {
        const cartographic = ellipsoid.cartesianToCartographic(intersection);
        const lat = Cesium.Math.toDegrees(cartographic.latitude);
        const lon = Cesium.Math.toDegrees(cartographic.longitude);
        if (!firstPoint) {
          firstPoint = { lat, lon };
        } else {
          secondPoint = { lat, lon };
          handlers.destroy();
          const west = Math.min(firstPoint.lon, secondPoint.lon);
          const east = Math.max(firstPoint.lon, secondPoint.lon);
          const south = Math.min(firstPoint.lat, secondPoint.lat);
          const north = Math.max(firstPoint.lat, secondPoint.lat);
          rectangleEntity.rectangle.coordinates = Cesium.Rectangle.fromDegrees(
            west,
            south,
            east,
            north
          );
          tool.mouseTooltip({}, true);
          tool.cesiumCursorConvert("default"); // 修改鼠标手势
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    handlers.setInputAction(function (movement: { endPosition: any }) {
      var cartesian = tool.getCatesian3FromPX(movement.endPosition);
      const obj = {
        position: cartesian,
        text: "左键选点 结束绘制",
      };
      tool.mouseTooltip(obj);
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    drawLayer.entities.add(rectangleEntity);
  }

  static markFeatureLine(type: string) {
    const viewer = window.viewer;
    //   var time = [];
    // for (let i in  globalData.line) {
    //   time.push(i);
    // }
    if (type == "HermiteSpline") {
      console.log(globalData.line);
      var controls = Cesium.Cartesian3.fromDegreesArray(globalData.line);
      console.log(controls);

      var spline = Cesium.HermiteSpline.createNaturalCubic({
        times: [0, 1, 2, 3, 4],
        points: controls,
      });
      viewer.entities.add({
        polyline: {
          positions: controls,
          width: 3,
          material: Cesium.Color.GREENYELLOW,
        },
      });

      var positions = [];
      for (var i = 0; i <= 10; i++) {
        var cartesian3 = spline.evaluate(i / 10);
        positions.push(cartesian3);
      }

      viewer.entities.add({
        name: "HermiteSpline",
        polyline: {
          positions: positions,
          width: 3,
          material: Cesium.Color.RED,
        },
      });
    } else {
    }
  }
}
export default markTool;
