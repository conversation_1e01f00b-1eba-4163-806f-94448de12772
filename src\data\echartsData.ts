const echartsData = {
  // Pie Chart (饼图)
  pie: {
    title: {
      text: '军事装备分布',
      subtext: '装备类型占比',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
    },
    series: [
      {
        name: '装备类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 42, name: '战斗机' },
          { value: 28, name: '坦克' },
          { value: 18, name: '反导车' },
          { value: 15, name: '导弹' },
          { value: 12, name: '指挥中心' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  },
  // Basic Bar Chart (基础柱状图)
  bar: {
    title: {
      text: '坐标点高度分布',
      left: 'center',
    },
    xAxis: {
      type: 'category',
      data: [
        '指挥中心',
        '坦克营',
        '反导车',
        '战斗机1',
        '战斗机2',
        '导弹1',
        '导弹2',
      ],
    },
    yAxis: {
      type: 'value',
      name: '高度(米)',
    },
    series: [
      {
        data: [0, 0, 0, 3000, 3500, 1000, 2500],
        type: 'bar',
        itemStyle: {
          color: function (params: any) {
            const colors = [
              '#c23531',
              '#c23531',
              '#c23531',
              '#2f4554',
              '#2f4554',
              '#61a0a8',
              '#61a0a8',
            ];
            return colors[params.dataIndex];
          },
        },
      },
    ],
  },
  // Basic Line Chart (基础折叠图)
  line: {
    title: {
      text: '飞行路径高度变化',
      left: 'center',
    },
    xAxis: {
      type: 'category',
      data: ['起点', '点1', '点2', '点3', '点4', '点5', '终点'],
    },
    yAxis: {
      type: 'value',
      name: '高度(米)',
    },
    series: [
      {
        name: '飞行高度',
        data: [3000, 3000, 3000, 3500, 4000, 3800, 3000],
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#2f4554',
          width: 3,
        },
        symbol: 'circle',
        symbolSize: 8,
      },
    ],
  },
  // Horizontal Bar Chart (条形图)
  hbar: {
    title: {
      text: '军事单位数量',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
      name: '数量',
    },
    yAxis: {
      type: 'category',
      data: [
        '红军指挥中心',
        '红军坦克营',
        '红军空军指挥部',
        '蓝军战斗机',
        '防空导弹',
        '反导无人车',
      ],
    },
    series: [
      {
        name: '装备数量',
        type: 'bar',
        data: [1, 5, 1, 8, 12, 3],
        itemStyle: {
          color: function (params: any) {
            // 红军单位使用红色，蓝军单位使用蓝色，其他使用黄色
            const colors = [
              '#c23531',
              '#c23531',
              '#c23531',
              '#2f4554',
              '#61a0a8',
              '#d48265',
            ];
            return colors[params.dataIndex];
          },
        },
      },
    ],
  },
  // Basic Area Chart (基础面积图)
  area: {
    title: {
      text: '雷达覆盖范围',
      left: 'center',
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [
        '0°',
        '45°',
        '90°',
        '135°',
        '180°',
        '225°',
        '270°',
        '315°',
        '360°',
      ],
    },
    yAxis: {
      type: 'value',
      name: '覆盖距离(km)',
    },
    series: [
      {
        name: '雷达覆盖',
        data: [35, 40, 45, 40, 35, 30, 35, 40, 35],
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255,0,0,0.5)', // 红色半透明
              },
              {
                offset: 1,
                color: 'rgba(255,0,0,0.1)',
              },
            ],
          },
        },
        lineStyle: {
          color: '#c23531',
          width: 2,
        },
      },
    ],
  },
  // Stacked Line Chart (折线图堆叠)
  sline: {
    title: {
      text: '军事单位活动轨迹',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: [
        '红军指挥中心',
        '红军坦克营',
        '红军空军',
        '蓝军战斗机',
        '蓝军无人机',
      ],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['0时', '4时', '8时', '12时', '16时', '20时', '24时'],
    },
    yAxis: {
      type: 'value',
      name: '活动强度',
    },
    series: [
      {
        name: '红军指挥中心',
        type: 'line',
        stack: '总量',
        data: [12, 13, 10, 13, 9, 23, 21],
        lineStyle: { color: '#c23531' },
        areaStyle: { color: '#c23531', opacity: 0.3 },
      },
      {
        name: '红军坦克营',
        type: 'line',
        stack: '总量',
        data: [22, 18, 19, 23, 29, 33, 31],
        lineStyle: { color: '#d48265' },
        areaStyle: { color: '#d48265', opacity: 0.3 },
      },
      {
        name: '红军空军',
        type: 'line',
        stack: '总量',
        data: [15, 23, 20, 15, 19, 33, 41],
        lineStyle: { color: '#91c7ae' },
        areaStyle: { color: '#91c7ae', opacity: 0.3 },
      },
      {
        name: '蓝军战斗机',
        type: 'line',
        stack: '总量',
        data: [32, 33, 30, 33, 39, 33, 32],
        lineStyle: { color: '#2f4554' },
        areaStyle: { color: '#2f4554', opacity: 0.3 },
      },
      {
        name: '蓝军无人机',
        type: 'line',
        stack: '总量',
        data: [82, 93, 90, 93, 129, 133, 132],
        lineStyle: { color: '#61a0a8' },
        areaStyle: { color: '#61a0a8', opacity: 0.3 },
      },
    ],
  },
  // Line Chart with Area Highlight (折线图区域高亮)
  lareah: {
    title: {
      text: '军事单位活动范围',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['红军地面部队', '红军空中部队', '蓝军空中部队'],
      bottom: 0,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [
        '北部',
        '东北部',
        '东部',
        '东南部',
        '南部',
        '西南部',
        '西部',
        '西北部',
      ],
    },
    yAxis: {
      type: 'value',
      name: '活动频率',
    },
    series: [
      {
        name: '红军地面部队',
        type: 'line',
        stack: 'Total',
        emphasis: {
          focus: 'series',
        },
        areaStyle: {},
        data: [120, 132, 101, 134, 90, 230, 210, 120],
        symbolSize: 8,
      },
      {
        name: '红军空中部队',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series',
        },
        data: [45, 82, 191, 234, 290, 130, 110, 85],
      },
      {
        name: '蓝军空中部队',
        type: 'line',
        stack: 'Total',
        label: {
          show: true,
          position: 'top',
        },
        areaStyle: {},
        emphasis: {
          focus: 'series',
          label: {
            show: true,
            position: 'top',
            fontWeight: 'bold'
          }
        },
        data: [150, 232, 201, 154, 90, 130, 210, 180],
        symbolSize: 8
      },
    ],
  },
  // Doughnut Chart (环形图)
  doughnut: {
    title: {
      text: '红蓝军力量对比',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
    },
    legend: {
      top: 'bottom',
    },
    series: [
      {
        name: '军事力量',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 45, name: '红军地面部队', itemStyle: { color: '#c23531' } },
          { value: 30, name: '红军空中部队', itemStyle: { color: '#d48265' } },
          { value: 15, name: '红军指挥系统', itemStyle: { color: '#91c7ae' } },
          { value: 48, name: '蓝军空中部队', itemStyle: { color: '#2f4554' } },
          { value: 12, name: '蓝军指挥系统', itemStyle: { color: '#61a0a8' } },
        ],
      },
    ],
  },

  // Time Series Chart (时间序列图) - 事件维度
  timeSeries: {
    title: {
      text: '军事活动时间序列',
      subtext: '24小时军事活动频率',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
    },
    legend: {
      data: [
        '红军地面行动',
        '红军空中行动',
        '蓝军空中行动',
        '雷达探测事件',
        '导弹发射',
      ],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [
        '00:00',
        '02:00',
        '04:00',
        '06:00',
        '08:00',
        '10:00',
        '12:00',
        '14:00',
        '16:00',
        '18:00',
        '20:00',
        '22:00',
      ],
      name: '时间',
    },
    yAxis: {
      type: 'value',
      name: '事件频率',
    },
    series: [
      {
        name: '红军地面行动',
        type: 'line',
        smooth: true,
        data: [3, 2, 1, 4, 8, 12, 10, 9, 11, 13, 7, 5],
        lineStyle: { color: '#c23531', width: 2 },
        symbol: 'circle',
        symbolSize: 6,
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' },
          ],
        },
      },
      {
        name: '红军空中行动',
        type: 'line',
        smooth: true,
        data: [1, 0, 2, 5, 7, 8, 9, 10, 6, 4, 3, 2],
        lineStyle: { color: '#d48265', width: 2 },
        symbol: 'circle',
        symbolSize: 6,
      },
      {
        name: '蓝军空中行动',
        type: 'line',
        smooth: true,
        data: [0, 1, 3, 6, 9, 11, 10, 8, 7, 5, 4, 2],
        lineStyle: { color: '#2f4554', width: 2 },
        symbol: 'circle',
        symbolSize: 6,
      },
      {
        name: '雷达探测事件',
        type: 'line',
        smooth: true,
        data: [5, 4, 6, 8, 12, 15, 14, 16, 13, 10, 8, 6],
        lineStyle: { color: '#61a0a8', width: 2 },
        symbol: 'circle',
        symbolSize: 6,
      },
      {
        name: '导弹发射',
        type: 'line',
        smooth: true,
        data: [0, 0, 0, 1, 2, 3, 1, 2, 1, 0, 0, 0],
        lineStyle: { color: '#91c7ae', width: 2 },
        symbol: 'circle',
        symbolSize: 6,
      },
    ],
  },

  // Scatter Chart (散点图) - 空间维度
  scatter: {
    title: {
      text: '军事单位地理分布',
      subtext: '基于经纬度坐标',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
      formatter: function (params: any) {
        return (
          '坐标: (' +
          params.value[0].toFixed(2) +
          ', ' +
          params.value[1].toFixed(2) +
          ')<br/>' +
          '单位数量: ' +
          params.value[2] +
          '<br/>' +
          '类型: ' +
          params.seriesName
        );
      },
    },
    legend: {
      data: [
        '红军地面单位',
        '红军空中单位',
        '蓝军空中单位',
        '雷达站',
        '指挥中心',
      ],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      name: '经度',
      min: 120.5,
      max: 121.5,
      axisLabel: {
        formatter: '{value}°E',
      },
    },
    yAxis: {
      type: 'value',
      name: '纬度',
      min: 23.5,
      max: 24.5,
      axisLabel: {
        formatter: '{value}°N',
      },
    },
    series: [
      {
        name: '红军地面单位',
        type: 'scatter',
        symbolSize: function (data: number[]) {
          return Math.sqrt(data[2]) * 10;
        },
        emphasis: {
          focus: 'series',
          label: {
            show: true,
            formatter: function (param: any) {
              return param.seriesName;
            },
            position: 'top',
          },
        },
        itemStyle: {
          color: '#c23531',
        },
        data: [
          [120.88707, 24.14479, 5],
          [120.68707, 23.85479, 8],
          [120.78707, 23.95479, 3],
          [120.98707, 24.04479, 6],
          [120.78707, 24.24479, 4],
        ],
      },
      {
        name: '红军空中单位',
        type: 'scatter',
        symbolSize: function (data: number[]) {
          return Math.sqrt(data[2]) * 10;
        },
        emphasis: {
          focus: 'series',
          label: {
            show: true,
            formatter: function (param: any) {
              return param.seriesName;
            },
            position: 'top',
          },
        },
        itemStyle: {
          color: '#d48265',
        },
        data: [
          [120.95707, 24.24479, 3],
          [121.08707, 24.05479, 4],
          [121.18707, 23.95479, 2],
          [121.28707, 23.85479, 5],
        ],
      },
      {
        name: '蓝军空中单位',
        type: 'scatter',
        symbolSize: function (data: number[]) {
          return Math.sqrt(data[2]) * 10;
        },
        emphasis: {
          focus: 'series',
          label: {
            show: true,
            formatter: function (param: any) {
              return param.seriesName;
            },
            position: 'top',
          },
        },
        itemStyle: {
          color: '#2f4554',
        },
        data: [
          [121.38707, 23.75479, 7],
          [121.28707, 23.65479, 6],
          [121.18707, 23.55479, 5],
          [121.08707, 23.45479, 4],
        ],
      },
      {
        name: '雷达站',
        type: 'scatter',
        symbolSize: function (data: number[]) {
          return Math.sqrt(data[2]) * 10;
        },
        emphasis: {
          focus: 'series',
          label: {
            show: true,
            formatter: function (param: any) {
              return param.seriesName;
            },
            position: 'top',
          },
        },
        itemStyle: {
          color: '#61a0a8',
        },
        data: [
          [120.78707, 23.75479, 3],
          [120.88707, 23.95479, 4],
          [120.98707, 24.15479, 2],
        ],
      },
      {
        name: '指挥中心',
        type: 'scatter',
        symbolSize: function (data: number[]) {
          return Math.sqrt(data[2]) * 10;
        },
        emphasis: {
          focus: 'series',
          label: {
            show: true,
            formatter: function (param: any) {
              return param.seriesName;
            },
            position: 'top',
          },
        },
        itemStyle: {
          color: '#91c7ae',
        },
        data: [[120.88707, 23.84479, 10]],
      },
    ],
  },

  // Radar Chart (雷达图) - 属性维度
  radar: {
    title: {
      text: '军事单位属性对比',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
    },
    legend: {
      data: ['红军坦克', '红军战斗机', '蓝军战斗机', '防空导弹'],
      bottom: 0,
    },
    radar: {
      // shape: 'circle',
      indicator: [
        { name: '速度', max: 100 },
        { name: '火力', max: 100 },
        { name: '防御', max: 100 },
        { name: '机动性', max: 100 },
        { name: '探测能力', max: 100 },
        { name: '打击精度', max: 100 },
      ],
    },
    series: [
      {
        name: '军事单位属性',
        type: 'radar',
        emphasis: {
          lineStyle: {
            width: 4,
          },
        },
        data: [
          {
            value: [30, 95, 90, 40, 20, 85],
            name: '红军坦克',
            areaStyle: {
              color: 'rgba(194, 53, 49, 0.3)',
            },
            lineStyle: {
              color: '#c23531',
            },
          },
          {
            value: [95, 80, 40, 90, 75, 90],
            name: '红军战斗机',
            areaStyle: {
              color: 'rgba(212, 130, 101, 0.3)',
            },
            lineStyle: {
              color: '#d48265',
            },
          },
          {
            value: [100, 85, 45, 95, 80, 95],
            name: '蓝军战斗机',
            areaStyle: {
              color: 'rgba(47, 69, 84, 0.3)',
            },
            lineStyle: {
              color: '#2f4554',
            },
          },
          {
            value: [10, 90, 30, 20, 85, 100],
            name: '防空导弹',
            areaStyle: {
              color: 'rgba(97, 160, 168, 0.3)',
            },
            lineStyle: {
              color: '#61a0a8',
            },
          },
        ],
      },
    ],
  },
};

export default echartsData;