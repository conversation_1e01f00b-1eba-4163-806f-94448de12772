import * as Cesium from "cesium";

// another convert style pure function
const handleMouseMove = (viewer: Cesium.Viewer, movement: any) => {
  // 获取鼠标下的实体
  const pickedObject = viewer.scene.pick(movement.endPosition);

  // 如果当前鼠标下有点位，应用悬浮样式
  if (
    Cesium.defined(pickedObject) &&
    pickedObject.primitive instanceof Cesium.Billboard &&
    pickedObject.primitive.id
  ) {
    // 修改鼠标样式
    viewer.canvas.style.cursor = "pointer";
  } else {
    // 恢复默认鼠标样式
    viewer.canvas.style.cursor = "default";
  }
};

function addConvertPointEvent(viewer: Cesium.Viewer) {
  viewer.screenSpaceEventHandler.setInputAction(
    (movement: any) => handleMouseMove(viewer, movement),
    Cesium.ScreenSpaceEventType.MOUSE_MOVE,
  );
}

export { addConvertPointEvent };
