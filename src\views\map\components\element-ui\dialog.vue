<template>
  <div class="system-menu-dialog-container">
    <el-dialog
      :title="state.dialog.title"
      v-model="state.dialog.isShowDialog"
      width="40%"
    >
      <el-form
        ref="ruleFormRef"
        :model="state.ruleForm"
        size="default"
        label-width="100px"
        :rules="rules"
      >
        <el-row :gutter="35">
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
            <el-form-item label="地名:" prop="userName">
              <el-input
                v-model="state.ruleForm.geoCoder"
                placeholder="请输入"
                clearable
              ></el-input>
              <el-select
                v-model="state.ruleForm.geoCoder"
                multiple
                filterable
                remote
                reserve-keyword
                placeholder="Please enter a keyword"
                :remote-method="remoteMethod"
                :loading="loading"
                style="width: 240px"
              >
                <el-option
                  v-for="item in state.options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            :xl="24"
            class="mb20 dialog_footer_wrap"
          >
            <span class="dialog_footer">
              <el-button @click="onCancel(ruleFormRef)" size="default"
                >取 消</el-button
              >
              <el-button
                type="primary"
                @click="onSubmit(ruleFormRef)"
                size="default"
                >确 定</el-button
              >
            </span>
          </el-col>
        </el-row>
      </el-form>
      <template #footer> </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="systemMenuDialog">
import { reactive, onMounted, ref } from "vue";
import { FormInstance } from "element-plus";

// 定义子组件向父组件传值/事件
const emit = defineEmits(["refresh", "submit"]);

// 定义变量内容
const loading = ref(false);
const list = ref([]);

const state = reactive({
  ruleForm: {
    geoCoder: "",
  },
  options: [], // 上级菜单数据
  dialog: {
    isShowDialog: false,
    type: "",
    title: "",
    submitTxt: "",
  },
});
const ruleFormRef = ref();

const rules = reactive({
  userName: [{ required: true, message: "请输入名称", trigger: "blur" }],
});

const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
      state.options = list.value.filter((item) => {
        return item.label.toLowerCase().includes(query.toLowerCase());
      });
    }, 200);
  } else {
    state.options = [];
  }
};

// 打开弹窗
const openDialog = (type: string, row?: any) => {
  state.dialog.title = "地名查找";
  state.dialog.submitTxt = "确 定";
  state.ruleForm = {
    geoCoder: "",
  };
  state.dialog.type = type;
  state.dialog.isShowDialog = true;
};
// 关闭弹窗
const closeDialog = () => {
  state.dialog.isShowDialog = false;
};

// 取消
const onCancel = (formEl: FormInstance | undefined) => {
  formEl && formEl.resetFields();
  closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      emit("submit", {
        ...state.ruleForm,
      });
      closeDialog(); // 关闭弹窗
    } else {
      console.log("error submit!", fields);
    }
  });
};
// 页面加载时
onMounted(() => {});

// 暴露变量
defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped></style>
