<script setup lang="ts">
// import { initMap } from "./utils/initMap";

// const viewerContainer = ref<HTMLElement | null>(null);
// onMounted(() => {
//   initMap(viewerContainer);
// });
</script>

<template>
  <!-- <div class="mainContainer">
    <div id="cesium-viewer" ref="viewerContainer"></div>
  </div> -->
  <RouterView />
</template>

<style>
/* cesium 实例全屏样式处理 */
#cesium-viewer,
.mainContainer {
  height: 100%;
  width: 100%;
  /* overflow: hidden; */
}

/* 隐藏 cesium 图片， 右下角全屏按钮 */
.cesium-widget-credits,
.cesium-viewer-fullscreenContainer {
  display: none !important;
}
</style>
