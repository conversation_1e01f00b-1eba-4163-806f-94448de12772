import commonjs from "@rollup/plugin-commonjs";
import vue from "@vitejs/plugin-vue";
import path from "path";
import AutoImport from "unplugin-auto-import/vite";
import IconsResolver from "unplugin-icons/resolver";
import Icons from "unplugin-icons/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import Components from "unplugin-vue-components/vite";
import { defineConfig, loadEnv } from "vite";
import cesium from "vite-plugin-cesium";
import requireTransform from "vite-plugin-require-transform";

const isProd = process.env.NODE_ENV === "production" ? true : false;

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd());
  const pathSrc = path.resolve(__dirname, "./");
  const { VITE_APP_ENV, VITE_APP_BASE_URL, VITE_PORT } = env;
  return {
    base: VITE_APP_ENV === "production" ? "/" : "/", // 例子:env.VITE_APP_BASE_URL || '/'
    plugins: [
      commonjs() as any,
      vue(),
      cesium(),
      AutoImport({
        imports: ["vue", "vue-router"],
        resolvers: [
          ElementPlusResolver(),
          IconsResolver({
            prefix: "Icon",
          }),
        ],
        dts: path.resolve(pathSrc, "auto-imports.d.ts"),
      }),
      Components({
        resolvers: [
          ElementPlusResolver(),
          IconsResolver({
            enabledCollections: ["ep"],
          }),
        ],
        dts: path.resolve(pathSrc, "components.d.ts"),
      }),
      Icons({
        autoInstall: true,
      }),
      //配置require
      requireTransform({
        fileRegex: /.js$|.vue$|.png$|.ts$|.jpg$|.svg$|.webp$/,
      }),
    ],
    // 设置scss的api类型为modern-compiler
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ["legacy-js-api"],
          api: "modern-compiler",
        },
      },
    },
    resolve: {
      alias: {
        //别名配置
        "~": path.resolve(__dirname, "./"), //配置的别名
        "@": path.resolve(__dirname, "./src"),
      },
      extensions: [
        ".mjs",
        ".js",
        ".mts",
        ".ts",
        ".jsx",
        ".tsx",
        ".json",
        ".vue",
      ],
    },
    rules: [
      {
        test: /\.(glb)$/,
        loader: "url-loader",
      },
    ],
    server: {
      host: "0.0.0.0", //本地地址
      port: Number(VITE_PORT), //自定义端口
      https: false, //false关门https,true为开启
      open: true,
      hot: true,
      proxy: {
        "/Google": {
          target: "http://*************:86",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/Google/, "Google"),
        },
        "/city": {
          target: "http://localhost:80",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/city/, "city"),
        },
        "/zhuji": {
          target: "http://*************:86",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/zhuji/, "zhuji"),
        },
        "/zj": {
          target: "http://localhost:80",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/zj/, "zj"),
        },
        "/tdtzj": {
          target: "http://localhost:80",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/tdtzj/, "tdtzj"),
        },
        "/jpg": {
          target: "http://localhost:80",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/jpg/, "jpg"),
        },
        "/vec": {
          target: "http://localhost:80",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/vdc/, "vec"),
        },
        "/gaode_yingxiang": {
          target: "http://localhost:80",
          changeOrigin: true,
          rewrite: (path) =>
            path.replace(/^\/gaode_yingxiang/, "gaode_yingxiang"),
        },
        "/geoserver": {
          target: "http://*************:8080",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/geoserver/, "geoserver"),
        },
      },
    },
    /** 混淆器 */
    // esbuild: {
    //   /** 打包时移除 console.log */
    //   pure: ['console.log'],
    //   /** 打包时移除 debugger */
    //   drop: ['debugger'],
    //   /** 打包时移除所有注释 */
    //   legalComments: 'none',
    // },
    build: {
      minify: "terser",
      sourcemap: "inline",
      terserOptions: {
        // compress: {
        //   // 打包时清除 console 和 debug 相关代码
        //   drop_console: true,
        //   drop_debugger: true,
        // },
        compress: isProd,
        mangle: isProd,
      },
    },
    vue: {
      compilerOptions: {
        optimizeSSR: false, // 避免在开发阶段过度优化模板编译，利于调试
      },
    },
    // 解决打包页面白板
    publicPath: "./",
    devtool: "source map",

    transpileDependencies: true,
    lintOnSave: false,
  };
});
