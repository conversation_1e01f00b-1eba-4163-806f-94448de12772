import * as Cesium from "cesium";

const layerWMTS = new Cesium.WebMapTileServiceImageryProvider({
  url: "http://192.168.55.128:8080/geoserver/map/gwc/service/wmts",
  layer: "map:line_shi", 
  style: "", 
  format: "image/png", 
  tileMatrixLabels: [
    "EPSG:4326:0",
    "EPSG:4326:1",
    "EPSG:4326:2",
    "EPSG:4326:3",
    "EPSG:4326:4",
    "EPSG:4326:5",
    "EPSG:4326:6",
    "EPSG:4326:7",
    "EPSG:4326:8",
    "EPSG:4326:9",
    "EPSG:4326:10",
    "EPSG:4326:11",
    "EPSG:4326:12",
    "EPSG:4326:13",
    "EPSG:4326:14",
    "EPSG:4326:15",
    "EPSG:4326:16",
    "EPSG:4326:17",
    "EPSG:4326:18",
    "EPSG:4326:19",
    "EPSG:4326:20",
    "EPSG:4326:21",
  ],
  tilingScheme: new Cesium.GeographicTilingScheme({
    numberOfLevelZeroTilesX: 2,
    numberOfLevelZeroTilesY: 1,
  }),
  // rectangle: Cesium.Rectangle.fromDegrees(
  //   73.45100463547345,
  //   18.16324718768713,
  //   134.9767976465501,
  //   53.531943151489806
  // ),
  tileMatrixSetID: "EPSG:4326", //切片策略，和选择的切片策略相同
});

const county = () => {
  const viewer = window.viewer;
  const layer = new Cesium.WebMapServiceImageryProvider({
    url: "http://127.0.0.1:8080/geoserver/kzzk/wms?",
    layers: "kzzk:county",
    parameters: {
      REQUEST: "GetMap",
      format: "image/png",
      srs: "EPSG:4326",
      styles: "",
      service: "WMS",
      VERSION: "1.1.1",
      transparent: true,
    },
    rectangle: Cesium.Rectangle.fromDegrees(-180, -90, 180, 90),
    // tilingScheme: new Cesium.WebMercatorTilingScheme(),
  });
  viewer.imageryLayers.addImageryProvider(layer);
};
export {county}
