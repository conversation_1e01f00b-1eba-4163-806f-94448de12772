// @ts-nocheck
/*
 * @Author: Yang
 * @Date: 2024-08-09 14:21:11
 * @LastEditors: Yang
 * @LastEditTime: 2024-08-12 13:54:03
 * @Description: 高德瓦片纠偏
 */
import {
  WebMercatorProjection,
  WebMercatorTilingScheme,
  Math,
  Cartographic,
  Cartesian2,
} from "cesium"; // 导入所需的 Cesium 模块
import CoordTransform from "@/components/tool/CoordTransform";

class AmapMercatorTilingScheme extends WebMercatorTilingScheme {
  constructor() {
    super();

    let projection = new WebMercatorProjection(); // 创建 WebMercatorProjection 对象用于进行投影转换

    // 重写 WebMercatorProjection 的 project 方法，实现从 WGS84 坐标系转换到 GCJ02 坐标系，再进行墨卡托投影
    this._projection.project = function (cartographic, result) {
      // 调用坐标转换工具类的 WGS84ToGCJ02 方法将经纬度转换为 GCJ02 坐标系下的经纬度
      result = CoordTransform.WGS84ToGCJ02(
        Math.toDegrees(cartographic.longitude),
        Math.toDegrees(cartographic.latitude)
      );
      // 将转换后的 GCJ02 坐标系下的经纬度转换为弧度，并进行墨卡托投影
      result = projection.project(
        new Cartographic(Math.toRadians(result[0]), Math.toRadians(result[1]))
      );
      return new Cartesian2(result.x, result.y); // 返回墨卡托投影后的结果
    };

    // 重写 WebMercatorProjection 的 unproject 方法，实现从墨卡托投影转换回 WGS84 坐标系
    this._projection.unproject = function (cartesian, result) {
      // 将墨卡托投影的坐标转换为经纬度
      let cartographic = projection.unproject(cartesian);
      // 调用坐标转换工具类的 GCJ02ToWGS84 方法将 GCJ02 坐标系下的经纬度转换为 WGS84 坐标系下的经纬度
      result = CoordTransform.GCJ02ToWGS84(
        Math.toDegrees(cartographic.longitude),
        Math.toDegrees(cartographic.latitude)
      );
      return new Cartographic(
        Math.toRadians(result[0]),
        Math.toRadians(result[1])
      ); // 返回 WGS84 坐标系下的结果
    };
  }
}

export default AmapMercatorTilingScheme; // 导出自定义的高德地图墨卡托投影切片方案
