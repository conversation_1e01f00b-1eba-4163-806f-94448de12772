<template>
  <el-dialog
    v-model="isShow"
    title="区域信息"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :before-close="close"
    class="region-info-popup"
    :append-to-body="true"
  >
    <div v-if="currentRegion">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="区域ID">{{
          currentRegion.id
        }}</el-descriptions-item>
        <el-descriptions-item label="名称">{{
          currentRegion.name
        }}</el-descriptions-item>
        <el-descriptions-item label="级别">{{
          currentRegion.level
        }}</el-descriptions-item>
        <el-descriptions-item v-if="currentRegion.code" label="代码">{{
          currentRegion.code
        }}</el-descriptions-item>
        <el-descriptions-item v-if="currentRegion.population" label="人口">{{
          currentRegion.population
        }}</el-descriptions-item>
        <el-descriptions-item v-if="currentRegion.area" label="面积"
          >{{ currentRegion.area }} km²</el-descriptions-item
        >
      </el-descriptions>
    </div>
    <div v-else class="no-data">
      <p>暂无区域信息</p>
    </div>
    <!-- <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="flyToRegion">定位</el-button>
      </span>
    </template> -->
  </el-dialog>
</template>

<script lang="ts" setup>
import * as Cesium from "cesium";

import { ref } from "vue";

import GeoJsonLoader from "@/utils/geoJsonLoader";

// 区域信息接口
interface RegionInfo {
  id: string;
  name: string;
  level: string;
  code?: string;
  population?: number;
  area?: number;
  [key: string]: any;
}

// 状态
const isShow = ref(false);
const currentRegion = ref<RegionInfo | null>(null);
const currentDataSource = ref<Cesium.DataSource | null>(null);
const currentEntity = ref<Cesium.Entity | null>(null);

/**
 * 打开弹窗
 * @param region 区域信息
 * @param dataSource 数据源
 * @param entity 实体对象（可选）
 */
const open = (
  region: RegionInfo,
  dataSource: Cesium.DataSource,
  entity?: Cesium.Entity,
) => {
  currentRegion.value = region;
  currentDataSource.value = dataSource;
  isShow.value = true;

  // 如果提供了实体对象，保存它以便在关闭弹窗时取消高亮
  if (entity) {
    currentEntity.value = entity;
  } else {
    // 如果没有提供实体对象，尝试从数据源中查找匹配的实体
    const entities = dataSource.entities.values;
    for (let i = 0; i < entities.length; i++) {
      const e = entities[i];
      if (e.name === region.name || e.id === region.id) {
        currentEntity.value = e;
        break;
      }
    }
  }
};

/**
 * 关闭弹窗
 */
const close = () => {
  // 取消区域高亮
  if (currentEntity.value) {
    GeoJsonLoader.unhighlightRegion(currentEntity.value);
    currentEntity.value = null;
  }

  isShow.value = false;
};

/**
 * 飞行到区域
 */
const flyToRegion = () => {
  if (currentDataSource.value) {
    const viewer = window.viewer;
    // 使用改进的定位方法
    GeoJsonLoader.flyToGeoJson(viewer, currentDataSource.value);
  }
  close();
};

// 暴露方法
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.region-info-popup {
  position: fixed;
  left: 20px;
  bottom: 20px;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #909399;
}

:deep(.el-dialog__body) {
  padding: 10px 20px;
}

:deep(.el-descriptions__label) {
  width: 80px;
}
</style>
