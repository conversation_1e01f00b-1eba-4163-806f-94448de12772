import * as Cesium from "cesium";

import { createImageryLayers } from "@/config/map-baselayer";
import { InitialEnum } from "~/src/utils/enum";

export const initMap = async (elRef: any) => {
  // if (window.viewer) return;
  const { imgLayer } = createImageryLayers();
  // 取消请求 cesium 官方瓦片 token
  /*  Cesium.Ion.defaultAccessToken =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI3ZWY4NGViYi05ODMyLTQzOTMtODRmMS04ZjEzZjEyYmNlY2EiLCJpZCI6MTY2Mzc2LCJpYXQiOjE2OTQ2NTkyOTR9.F37r6bfgntGwb_8Z4fpDCuvusjDlvAXLEue9DrHEyFA"; */

  const viewer: Cesium.Viewer = new Cesium.Viewer(elRef.value as HTMLElement, {
    baseLayer: imgLayer, //自定义图层（agric），默认是必应影像图层
    selectionIndicator: false, // 不显示指示器小部件
    sceneModePicker: false, // 是否显示投影方式控件 （二三维切换事件）
    navigationHelpButton: false, // 是否显示导航栏帮助信息控件
    homeButton: false, // 是否显示Home按钮
    geocoder: false, // 是否显示地名查找控件
    baseLayerPicker: false, // 是否显示图层选择控件
    infoBox: false, // 是否显示点击要素之后显示的信息
    scene3DOnly: false, // 每个几何实例将只能以3D渲染以节省GPU内存
    sceneMode: 3, // 初始场景模式 1 2D模式 2 2D循环模式 3 3D模式  Cesium.SceneMode
    // timeline: false,
    // animation: false,
    // terrainProvider:Cesium.createWorldTerrain()
  });
  //   mapViewer = viewer;
  //   isViewer.value = true;

  // viewer.imageryLayers.add(window.cia_c);
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(
      InitialEnum.longitude,
      InitialEnum.latitude,
      InitialEnum.height,
    ),
  });
  Cesium.RequestScheduler.maximumRequestsPerServer = 60; // 设置cesium请求调度器的最大并发数量
  Cesium.RequestScheduler.throttleRequests = false; //关闭请求调度器的请求节流
  viewer.scene.debugShowFramesPerSecond = false; //显示帧 FPS
  viewer.scene.globe.depthTestAgainstTerrain = false; //开启地形深度检测
  viewer.animation.container.style.visibility = "hidden";
  viewer.timeline.container.style.visibility = "hidden";

  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 350; // 最小缩放高度（米）

  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 50000000; // 最大缩放高度（米）
  viewer.scene.screenSpaceCameraController.zoomEventTypes = [
    Cesium.CameraEventType.WHEEL,
    Cesium.CameraEventType.MIDDLE_DRAG,
    Cesium.CameraEventType.PINCH,
  ];
  return viewer;
};
