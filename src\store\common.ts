
import { defineStore } from 'pinia'
import * as <PERSON>sium from "cesium";

/**
 * Use store
 */
interface CommonState {
  viewer: null | Cesium.Viewer; 
}


export const useCommonStore = defineStore('common', {
  state: ():CommonState => ({
    viewer: null
  }),
  getters: {
    getIsViewer: (state) => state.viewer !== null,
    getViewer: (state) => state.viewer
  },
  actions: {
    setViewer(viewer: Cesium.Viewer) {
      this.viewer = viewer
    }
  },
})