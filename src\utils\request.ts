import axios from 'axios';
const service = axios.create({
  // baseURL: 'http://192.168.1.129:86',
  timeout: 5000,
});

// 不需要token验证的 接口白名单
// const APIWhite = ['users/login']
// 请求拦截 设置统一header
service.interceptors.request.use(
  (config: any) => {
    return config;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);
// 响应拦截 401 token过期等处理
service.interceptors.response.use(
  (response: any) => {
    return response;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);
//  封装具体的请求方法: 四种请求类型
export const get = (url: string, params: any) => {
  return service.get(url, { params });
};

export const post = (url: string, data: any) => {
  return service.post(url, data);
};

export const put = (url: string, data: any) => {
  return service.put(url, data);
};

export const del = (url: string, data: any) => {
  return service.delete(url, data);
};
export default service;
