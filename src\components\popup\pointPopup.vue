<template>
    <div class="popup" v-if="isShow">
        <div class="title">
            <span class="float-left ml25">属性编辑</span>
            <span class="red float-right mr6 cursor-pointer" @click="close(ruleFormRef)">
                <i class="iconfont gis-shanchu"></i>
            </span>
        </div>
        <div class="content">
            <el-form ref="ruleFormRef" class="pointFrom" :model="editFrom" label-width="80">
                <el-form-item label="点位名称 :" prop="name">
                    <el-input v-model="editFrom.name" @keyup.enter="changName" @blur="changName" placeholder="请输入点位名称" />
                </el-form-item>
                <el-form-item label="位置信息 :" prop="position">
                    <el-input v-model="editFrom.position.lon" @keyup.enter="changPosition" min="0" max="180"
                        @blur="changPosition">
                        <template #prepend>经度</template>
                        <template #append>°</template>
                    </el-input>
                    <el-input class="mt6"v-model="editFrom.position.lat" @keyup.enter="changPosition" min="0" max="180"
                        @blur="changPosition">
                        <template #prepend>纬度</template>
                        <template #append>°</template>
                    </el-input>
                </el-form-item>
                <!-- <el-form-item label=" :" prop="position">
                    <el-input v-model="editFrom.position.lat" @keyup.enter="changPosition" min="0" max="90" @blur="changPosition"/>
                </el-form-item> -->
                <!-- <el-form-item label="颜色 :" prop="color">
                    <el-color-picker v-model="editFrom.color" size="large" @change="changeColor" />
                </el-form-item> -->
                <el-form-item label="缩放比例 :" prop="scale">
                    <el-input-number v-model="editFrom.scale" @blur="blurScale" @change="blurScale" :min="0.1" :max="2.0"
                        :step="0.1" :precision="1" />
                </el-form-item>
                <el-form-item label="旋转角度 :" prop="rotation">
                    <el-input-number v-model="editFrom.rotation" type="number" @change="blurRotation" @blur="blurRotation"
                        :min="0" :max="360" :step="1" :precision="2">
                        <template #suffix>°</template>
                    </el-input-number>
                </el-form-item>
                <!-- <el-form-item label="透 明 度 :" prop="alpha ">
                    <el-slider v-model="editFrom.alpha" :min="0.1" :max="1.0" :step="0.1" :precision="1" size="small" />
                </el-form-item> -->
                <!-- <el-form-item label="是否贴地 :" prop="heightReference">
                    <el-switch v-model="editFrom.heightReference" inline-prompt active-text="是" inactive-text="否" />
                </el-form-item> -->
                <el-form-item label="是否按视距显示 :" prop="distanceDisplayCondition">
                    <el-switch v-model="editFrom.distanceDisplayCondition" inline-prompt active-text="是" inactive-text="否"
                        @change="changDistance" />
                    <div class="distance" v-if="editFrom.distanceDisplayCondition">
                        <div>
                            <span>最小：</span>
                            <el-input-number class="number" @blur="changDistance" @change="changDistance"
                                v-model="editFrom.near" :min="0" :step="1" :precision="0">
                                <template #suffix>
                                    <span>米</span>
                                </template>
                            </el-input-number>
                        </div>
                        <div style="margin-top: 3px;">
                            <span>最大：</span>
                            <el-input-number class="number" @blur="changDistance" @change="changDistance"
                                v-model="editFrom.far" :min="0" :step="1" :precision="0">
                                <template #suffix>
                                    <span>米</span>
                                </template>
                            </el-input-number>
                        </div>
                    </div>
                </el-form-item>
                <!-- <el-form-item label="是否被遮挡 :" prop="distanceDisplayCondition">
                    <el-switch v-model="editFrom.distanceDisplayCondition" inline-prompt active-text="是"
                        inactive-text="否" />
                </el-form-item> -->
                <!-- <el-form-item label="实体点 :">
                    <div class="icon" v-for="item in svgCode">
                        <el-tooltip :content="item.name" placement="top" effect="customized">
                            <div class="svg" v-html="item.value" @click="changIcon(item.value)"></div>
                        </el-tooltip>
                    </div>
                </el-form-item> -->
            </el-form>
        </div>
    </div>
</template>

<script lang="ts" setup>
import * as Cesium from "cesium";
import bus from '~/src/utils/bus';
import { svgCode } from '@/components/point/svgSource';
import tool from "../tool/tool";
import { FormInstance } from "element-plus";
const entity = ref();
const isShow = ref(false);
const ruleFormRef = ref<FormInstance>()
interface EditFrom {
    name: string,
    color: string,
    scale: number,
    alpha: number,
    near: number,//最小视距
    far: number, //最大视距
    rotation: number,
    position: any,
    heightReference: boolean, // 贴地属性
    distanceDisplayCondition: boolean // 视距显示
}
const editFrom = reactive<EditFrom>({
    name: '',
    color: '409EFF',
    scale: 1,
    near: 0,
    far: 3000000,
    rotation: 0,
    position: null,
    alpha: 1,
    heightReference: true,
    distanceDisplayCondition: true,
})
const changeColor = (value: any) => {
    console.log("value", value);
}
const changIcon = (param: string) => {
    entity.value.billboard.image = new Cesium.CallbackProperty(function () {
        return tool.svgToDataUri(param);
    }, false);
}
//旋转角度改变后事件
const blurRotation = () => {
    // console.log("角度", editFrom.rotation);
    entity.value.billboard.rotation = Cesium.Math.toRadians(editFrom.rotation); //角度转换弧度
}
//缩放比例改变后事件
const blurScale = () => {
    // console.log("缩放", editFrom.scale);
    entity.value.billboard.scale = editFrom.scale;

}
//名称改变后事件
const changName = () => {
    console.log("名称", editFrom.name);
    if (entity.value.label) {
        entity.value.label.text = editFrom.name;
    } else if (editFrom.name) {
        entity.value.label = {
            text: editFrom.name,
            font: "10px sans-serif", //字体样式
            fillColor: Cesium.Color.BLACK, //字体颜色
            backgroundColor: new Cesium.Color(19, 159, 159, 0.6), //背景颜色
            showBackground: true, //是否显示背景颜色
            style: Cesium.LabelStyle.FILL, //label样式
            outlineWidth: 2,
            verticalOrigin: Cesium.VerticalOrigin.CENTER, //垂直位置
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER, //水平位置
            pixelOffset: new Cesium.Cartesian2(0, 28), //偏移
            disableDepthTestDistance: 99000000, //不受深度的影响
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(editFrom.near, editFrom.far),
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,  // 设置该位置与地形相适应
        }
    } else {
        entity.value.label = undefined
    }
}
// 修改点位信息
const changPosition = () => {
    let cartesian = Cesium.Cartesian3.fromDegrees(Number(editFrom.position.lon), Number(editFrom.position.lat))
    console.log(cartesian);
    entity.value.position = cartesian

}
//视距最大 小 值改变事件
const changDistance = (val: any) => {
    if (val) {
        entity.value.billboard.distanceDisplayCondition = new Cesium.DistanceDisplayCondition(editFrom.near, editFrom.far);
    } else {
        entity.value.billboard.distanceDisplayCondition = undefined
    }
}

const initEntity = (param: any) => {
    entity.value = param
    editFrom.name = param?.label?.text?._value || null
    editFrom.position = tool.transformCartesianToWGS84(param.position?._value) || { lon: '', lat: '' }
    editFrom.scale = param.billboard?.scale?._value || 1
    editFrom.rotation = param.billboard?.rotation?._value * (180 / Math.PI) || 0
    editFrom.distanceDisplayCondition = param.billboard?.distanceDisplayCondition ? true : false
    editFrom.near = param.billboard?.distanceDisplayCondition?._value._near || 0
    editFrom.far = param.billboard?.distanceDisplayCondition?._value._far || 3000000
    isShow.value = true
}
const close = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    isShow.value = false
}
bus.emit("editPointEntity", { entity: entity.value, isShow: true })
bus.on("editPointEntity", (res: any) => { if (res.isShow) { initEntity(res.entity) } else { close(ruleFormRef.value) } });

defineExpose({
    initEntity
})
</script>

<style lang="scss" scoped>
.popup {
    height: 100%;
    width: 300px;
    grid-row: 1;
    overflow: hidden;
    color: var(--gis-default-color);
    // background-image: linear-gradient(to right, rgba(23, 44, 63, 0.35), rgba(23, 44, 63, 0.35));
    background-image: linear-gradient(to right, rgba(15, 57, 115, 0.6), rgba(15, 57, 115, 0.6));

    .title {
        width: 100%;
        height: 26px;
        background-size: 100% 100%;
        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAAARCAYAAADzJPoFAAABrklEQVR4nO3cz0obURSA8S/BVtF3KG2lW/e6deFCaN30JUpRjJVY6NKCJUL6x7foposuSqG48xkqLgTxEapUXEQunMAwJJmbpdfvt5wZkjmZA4d7zp20lg8GAD+BdZp9BD4AO8Bh5epbYAn4O+kTTroZ3yBJuhfacZPbwP+MG34HvAC+1orFI+DIRy5JD8ewgJwBvYyoZ4F+rDje1s6tAq/NHUl6GNqVKA+Ai4yoU6vrFfAH+F47l9paC+aOJJWvWkCuga3MiNMqZC5aWleV409iRiJJKly7Ft4P4HdGyM+AvVix7NfObcecRJJUsHoBSTaBm4yQu1FI+jFDGUpzkm8mjSSVbVQBOY2i0CS1sD5HsdmsXbsGbJg7klSuUQWEeN/jMiPqlzFU/xXtr6pUhObNHUkq07gC8g/oZEbcj7ZVJwbxQ09jTiJJKtC4AkJs0T3OCDkNzHeB89gKXJWOL5o4klSeSQUkeRMvDTZ5H1t4e1FIhtKc5It5I0nlmWmIKP1dyeMpo35unkhS+VqDwcDHLEmaWlMLS5KkkWZWPvnDSJKml2Yg9rAkSdMB7gC1QTnm9AlvDgAAAABJRU5ErkJggg==);
        // background-image: url(data:image/png;base64,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);
    }
    .content{
        height: calc(100% - 26px);
        overflow-x: hidden;
    }

    .pointFrom {
        padding: 20px;

        :deep(.el-form-item__label) {
            display: grid;
            color: var(--gis-default-color);
            align-content: center;
            line-height: normal;
        }

        :deep(.el-slider__button) {
            width: 12px;
            height: 12px;
        }

        .svg {
            width: 32px;
            height: 32px;
            padding: 5px;
            cursor: pointer;

            // &:active {
            //     background-color: #ffffffa3;
            //     border: 1px solid #3b7cff;
            //     border-radius: 6px;
            // }
        }

        :deep(.el-input-group__append),
        :deep(.el-input-group__prepend) {
            padding: 0 3px;
        }

        .distance {
            margin-left: -30px;
            display: flex;
            flex-wrap: wrap;

            >span {
                width: 50px;
            }

            .number {
                width: calc(100% - 50px);
            }
        }

        :deep(.el-form-item) {
            margin-bottom: 8px;
        }
    }
}
</style>