<!-- 爆炸点 -->
<template>
  <div class="mainContainer">
    <div id="cesium-viewer" ref="viewerContainer"></div>
    <div class="fixed top-0 left-0">
      <el-button type="primary" @click="() => onInit(viewer!)"
        >渲染火焰</el-button
      >
      <el-button type="primary" @click="onClear">清除</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";

import { onBeforeUnmount, onMounted, ref } from "vue";

import FireEffect from "@/utils/FireEffect";
import { initMap } from "@/utils/initMap";

const viewerContainer = ref<HTMLElement | null>(null);
let viewer: Cesium.Viewer | null = null;
let fire: FireEffect | null = null;

const onInit = async (viewer: Cesium.Viewer) => {
  viewer.camera.flyTo({
    // 从以度为单位的经度和纬度值返回笛卡尔3位置。
    destination: Cesium.Cartesian3.fromDegrees(116.391, 39.9042, 5000),
    orientation: {
      direction: new Cesium.Cartesian3(
        0.7458181136018,
        -0.4270255968894706,
        0.5112773034515067,
      ),
      up: new Cesium.Cartesian3(
        -0.19274344830978868,
        0.5963500021825172,
        0.7792410654159365,
      ),
    },
    duration: 3, // 飞行时间（s）
  });
  await new Promise((resolve) => setTimeout(resolve, 3000));
  fire = new FireEffect(viewer);
};

const onClear = () => {
  if (!fire) return;
  fire.remove();
  fire = null;
};

onMounted(async () => {
  viewer = await initMap(viewerContainer);
});

onBeforeUnmount(() => {
  if (fire) {
    fire.remove();
  }
  if (viewer) {
    viewer.destroy();
  }
});
</script>

<style scoped>
.mainContainer {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
