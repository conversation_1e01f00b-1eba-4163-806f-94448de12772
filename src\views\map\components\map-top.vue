<script setup lang="ts">
import { ref, reactive } from "vue";
</script>
<template>
  <div class="map_top">
    <div class="sysName">模拟仿真三维平台</div>
  </div>
</template>
<style lang="scss" scoped>
.map_top {
  position: fixed;
  right: 4vw;
  left: 4vw;
  z-index: 1;
  top: 0;
  height: 70px;
  width: calc(100% - 8vw);
  background: url("@/assets/images/themebg.png") no-repeat center center;
  .sysName {
    width: 100%;
    padding-top: 15px;
    text-align: center;
    color: var(--gis-default-color);
    font-size: 24px;
    letter-spacing: 5px;
  }
}
</style>
