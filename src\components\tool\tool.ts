import * as Cesium from "cesium";
import bus from "~/src/utils/bus";
import pointHadler from "~/src/components/point/pointHadler";
import { InitialEnum } from "~/src/utils/enum";

class tool {
  // 地图缩放操作
  static zoom(param: string) {
    const viewer = window.viewer,
      position = viewer.camera.position;
    let cameraHeight =
      viewer.scene.globe.ellipsoid.cartesianToCartographic(position).height;
    let moveRate = cameraHeight / 2.0; // 每次放大 200 倍，参数可改
    if (param == "in") {
      viewer.camera.moveForward(moveRate);
    } else {
      viewer.camera.moveBackward(moveRate);
    }
  }
  // 地图复位操作
  static reset() {
    const viewer = window.viewer;
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(InitialEnum.longitude, InitialEnum.latitude, InitialEnum.height),
    });
  }
  static radarSolidScan(options: any) {
    const viewer = window.viewer;
    viewer.entities.add({
      name: String(new Date().valueOf()),
      position: Cesium.Cartesian3.fromDegrees(
        options.position[0],
        options.position[1]
      ),
      ellipsoid: {
        radii: new Cesium.Cartesian3(
          options.radius,
          options.radius,
          options.radius
        ),
        maximumCone: Cesium.Math.PI_OVER_TWO,
        material: options.color,
        outline: true,
        outlineColor: Cesium.Color.fromCssColorString("#00dcff82"),
        outlineWidth: 0.2,
      },
    });
  }
  static transformWGS84ToCartesian(
    position: { lng: number; lat: number; alt: number } | undefined,
    alt: number | undefined
  ) {
    const viewer = window.viewer;
    let convert = Cesium.Cartesian3.fromDegrees(
      position?.lon,
      position.lat,
      (position.alt = alt || position.alt),
      Cesium.Ellipsoid.WGS84
    );
    if (viewer) {
      return position ? convert : Cesium.Cartesian3.ZERO;
    }
  }
  static transformWGS84ArrayToCartesianArray(WSG84Arr: any[], alt: any) {
    const viewer = window.viewer;
    if (viewer && WSG84Arr) {
      var $this = this;
      return WSG84Arr
        ? WSG84Arr.map(function (item: any) {
            return $this.transformWGS84ToCartesian(item, alt);
          })
        : [];
    }
  }
  static transformCartesianToWGS84(cartesian: Cesium.Cartesian3) {
    const viewer = window.viewer;
    if (viewer && cartesian) {
      var ellipsoid = Cesium.Ellipsoid.WGS84;
      var cartographic = ellipsoid.cartesianToCartographic(cartesian);
      let position = {
        lon: Cesium.Math.toDegrees(cartographic.longitude),
        lat: Cesium.Math.toDegrees(cartographic.latitude),
        alt: cartographic.height,
      };
      return position;
    }
  }

  static transformCartesianArrayToWGS84Array(cartesianArr: any[]) {
    const viewer = window.viewer;
    if (viewer) {
      var $this = this;
      return cartesianArr
        ? cartesianArr.map(function (item: any) {
            return $this.transformCartesianToWGS84(item);
          })
        : [];
    }
  }
  static transformWGS84ToCartographic(position: any) {
    let convert = Cesium.Cartographic.fromDegrees(
      position.lon,
      position.lat,
      position.alt
    );
    return position ? convert : Cesium.Cartographic.ZERO;
  }

  static getCatesian3FromPX(px: Cesium.Cartesian2) {
    const viewer = window.viewer;
    if (viewer && px) {
      const viewer = window.viewer;
      var picks = viewer.scene.drillPick(px);
      var cartesian = null;
      var isOn3dtiles = false,
        isOnTerrain = false;
      // drillPick
      for (let i in picks) {
        let pick = picks[i];

        if (
          (pick && pick.primitive instanceof Cesium.Cesium3DTileFeature) ||
          (pick && pick.primitive instanceof Cesium.Cesium3DTileset) ||
          (pick && pick.primitive instanceof Cesium.Model)
        ) {
          isOn3dtiles = true;
        }
        if (isOn3dtiles) {
          viewer.scene.pick(px); // pick
          cartesian = viewer.scene.pickPosition(px);
          if (cartesian) {
            let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
            if (cartographic.height < 0) cartographic.height = 0;
            let lon = Cesium.Math.toDegrees(cartographic.longitude),
              lat = Cesium.Math.toDegrees(cartographic.latitude),
              height = cartographic.height;
            cartesian = this.transformWGS84ToCartesian({
              lon: lon,
              lat: lat,
              alt: height,
            });
          }
        }
      }
      // 地形
      let boolTerrain =
        viewer.terrainProvider instanceof Cesium.EllipsoidTerrainProvider;
      if (!isOn3dtiles && !boolTerrain) {
        var ray = viewer.scene.camera.getPickRay(px);
        if (!ray) return null;
        cartesian = viewer.scene.globe.pick(ray, viewer.scene);
        isOnTerrain = true;
      }
      // 地球
      if (!isOn3dtiles && !isOnTerrain && boolTerrain) {
        cartesian = viewer.scene.camera.pickEllipsoid(
          px,
          viewer.scene.globe.ellipsoid
        );
      }
      if (cartesian) {
        let position = this.transformCartesianToWGS84(cartesian);
        if (position.alt < 0) {
          cartesian = this.transformWGS84ToCartesian(position, 0.1);
        }
        return cartesian;
      }
      return false;
    }
  }
  /**
   * 获取84坐标的距离
   * @param {*} positions
   */
  static getPositionDistance(positions: string | any[]) {
    let distance = 0;
    for (let i = 0; i < positions.length - 1; i++) {
      let point1cartographic = this.transformWGS84ToCartographic(positions[i]);
      let point2cartographic = this.transformWGS84ToCartographic(
        positions[i + 1]
      );
      let geodesic = new Cesium.EllipsoidGeodesic();
      geodesic.setEndPoints(point1cartographic, point2cartographic);
      let s = geodesic.surfaceDistance;
      s = Math.sqrt(
        Math.pow(s, 2) +
          Math.pow(point2cartographic.height - point1cartographic.height, 2)
      );
      distance = distance + s;
    }
    return distance.toFixed(3);
  }
  /**
   * 计算一组坐标组成多边形的面积
   * @param {*} positions
   */
  static getPositionsArea(positions: any[]) {
    let result = 0;
    if (positions) {
      let h = 0;
      let ellipsoid = Cesium.Ellipsoid.WGS84;
      positions.push(positions[0]);
      for (let i = 1; i < positions.length; i++) {
        let oel = ellipsoid.cartographicToCartesian(
          this.transformWGS84ToCartographic(positions[i - 1])
        );
        let el = ellipsoid.cartographicToCartesian(
          this.transformWGS84ToCartographic(positions[i])
        );
        h += oel.x * el.y - el.x * oel.y;
      }
      result = Number(Math.abs(h).toFixed(2));
    }
    return result;
  }
  /**
   * @description: 水平测量距离
   * @param {*} options
   * @return {*}
   */
  static drawLineMeasureGraphics(options = {}) {
    const viewer = window.viewer,
      ids = new Date().getTime(),
      _drawLayer = new Cesium.CustomDataSource(ids.toString());
    viewer.dataSources.add(_drawLayer);
    tool.cesiumCursorConvert("pointer"); // 修改鼠标手势
    if (viewer && options) {
      let positions: any[] = [],
        _lineEntity = new Cesium.Entity(),
        $this = this,
        lineObj: Cesium.Entity,
        _handlers = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
      // 鼠标左键单击事件
      _handlers.setInputAction(function (movement: { position: any }) {
        var cartesian = $this.getCatesian3FromPX(movement.position);
        if (cartesian && cartesian.x) {
          if (positions.length == 0) {
            positions.push(cartesian.clone());

          }
          let obj = {
            id: ids,
            position: cartesian,
            point: {
              outlineColor: new Cesium.Color(0.6, 0.2, 0.4, 1),
              color: Cesium.Color.fromCssColorString("#59FF9B"),
              pixelSize: 10,
              outlineWidth: 0,
              distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                0,
                2000000
              ),
            },
            label: {
              text: tool.computePositionArrayDistance(positions),
              show: true,
              showBackground: true,
              font: "18px  sans-serif",
              pixelOffset: new Cesium.Cartesian2(0, 30),
              distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                0,
                10000000
              ),
            },
            entity: _drawLayer,
          };
          // 添加量测信息点
          pointHadler.addMeasureInfoPoint(obj);
          positions.push(cartesian);

          // var cartesian3 = new Cesium.Cartesian3(movement.position);

          var ellipsoid = Cesium.Ellipsoid.WGS84;
          var cartographic = ellipsoid.cartesianToCartographic(cartesian);

        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      // 监听鼠标移动事件
      _handlers.setInputAction(function (movement: { endPosition: any }) {
        var cartesian = $this.getCatesian3FromPX(movement.endPosition);

        const obj = {
          position: cartesian,
          text: "左键选点，右键结束",
        };
        tool.mouseTooltip(obj);
        if (positions.length >= 2) {
          if (cartesian && cartesian.x) {
            positions.pop();
            positions.push(cartesian);
          }
        }
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      // 监听右键结束事件
      _handlers.setInputAction(function (movement: { position: any }) {
        _handlers.destroy();
        _handlers = null;


        let cartesian = $this.getCatesian3FromPX(movement.position);

        let obj = {
          id: ids,
          position: cartesian,
          label: {
            text: tool.computePositionArrayDistance(positions),
            show: true,
            showBackground: true,
            font: "18px  sans-serif",
            pixelOffset: new Cesium.Cartesian2(0, 30),
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
              0,
              10000000
            ),
          },
          entity: _drawLayer,
        };
        pointHadler.addMeasureInfoPoint(obj, true);

        if (typeof options.callback === "function") {
          options.callback(
            $this.transformCartesianArrayToWGS84Array(positions),
            lineObj
          );
        }
        tool.cesiumCursorConvert("default");
        tool.mouseTooltip({}, true);
      }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

      _lineEntity.name = ids.toString();
      _lineEntity.polyline = {
        width: options?.width || 3,
        material:
          options?.material || Cesium.Color.fromCssColorString("#59FF9B"),
        clampToGround: options?.clampToGround || false,
      };
      _lineEntity.polyline.positions = new Cesium.CallbackProperty(function () {
        return positions;
      }, false);

      lineObj = _drawLayer.entities.add(_lineEntity);
    }
  }
  /**
   * 测面积
   * @param {*} options
   */
  static drawAreaMeasureGraphics(options = {}) {
    const viewer = window.viewer,
      ids = new Date().getTime(),
      _drawLayer = new Cesium.CustomDataSource(ids.toString());
    viewer.dataSources.add(_drawLayer);
    if (viewer && options) {
      var positions: any[] = [],
        polygon = new Cesium.PolygonHierarchy(),
        _polygonEntity = new Cesium.Entity(),
        $this = this,
        polyObj: null = null,
        _label = "",
        _handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
      // left
      _handler.setInputAction(function (movement: { position: any }) {
        var cartesian = $this.getCatesian3FromPX(movement.position);
        if (cartesian && cartesian.x) {
          if (positions.length == 0) {
            polygon.positions.push(cartesian.clone());
            positions.push(cartesian.clone());
          }
          positions.push(cartesian.clone());
          polygon.positions.push(cartesian.clone());

          if (!polyObj) create();
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      // mouse
      _handler.setInputAction(function (movement: { endPosition: any }) {
        var cartesian = $this.getCatesian3FromPX(movement.endPosition);
        if (positions.length >= 2) {
          if (cartesian && cartesian.x) {
            positions.pop();
            positions.push(cartesian);
            polygon.positions.pop();
            polygon.positions.push(cartesian);
          }
        }
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

      // right
      _handler.setInputAction(function (movement: { position: any }) {
        let cartesian = tool.getCatesian3FromPX(movement.position);
        _handler.destroy();
        positions.push(positions[0]);

        // 添加信息点
        // _addInfoPoint(positions[0]);
        let obj = {
          id: ids,
          position: cartesian,
          label: {
            text:
              (
                $this.getPositionsArea(
                  $this.transformCartesianArrayToWGS84Array(positions)
                ) / 1000000.0
              ).toFixed(4) + " k㎡",
            show: true,
            showBackground: true,
            font: "18px sans-serif",
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
              0,
              80000000
            ),
            pixelOffset: new Cesium.Cartesian2(20, -20), //left top
          },
          entity: _drawLayer,
        };
        pointHadler.addMeasureInfoPoint(obj, true);
        if (typeof options.callback === "function") {
          options.callback(
            $this.transformCartesianArrayToWGS84Array(positions),
            polyObj
          );
        }
      }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

      function create() {
        _polygonEntity.name = ids;
        _polygonEntity.polyline = {
          width: 3,
          material: Cesium.Color.fromCssColorString("#59FF9B"),
          clampToGround: options.clampToGround || false,
        };

        _polygonEntity.polyline.positions = new Cesium.CallbackProperty(
          function () {
            return positions;
          },
          false
        );

        _polygonEntity.polygon = {
          hierarchy: new Cesium.CallbackProperty(function () {
            return polygon;
          }, false),

          material: Cesium.Color.WHITE.withAlpha(0.1),
          clampToGround: options.clampToGround || false,
        };

        polyObj = _drawLayer.entities.add(_polygonEntity);
      }
    }
  }
  /**
   * @description: 方位角测量
   * @return {*}
   */
  static azimuthAngle(options?: any) {
    tool.cesiumCursorConvert("pointer");
    const viewer = window.viewer;
    let positions: any[] = [],
      ids = new Date().getTime(),
      lineEntity = new Cesium.Entity({
        polyline: new Cesium.PolylineGraphics({
          width: options?.width || 3,
          material:
            options?.material || Cesium.Color.fromCssColorString("#59FF9B"),
          clampToGround: options?.clampToGround || false,
        }),
      }),
      $this = this,
      lineObj: Cesium.Entity,
      eventBus: any,
      angleLayer: Cesium.Entity.ConstructorOptions,
      leftClickCartesian: boolean | Cesium.Cartesian3 | null | undefined,
      drawLayer = new Cesium.CustomDataSource(ids.toString()),
      handlers = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    viewer.dataSources.add(drawLayer);
    bus.on("mouseMove", (res: any) => {
      eventBus = res;
    });

    // 监听左键单击选点
    handlers.setInputAction(function (movement: { position: any }) {
      let cartesian = (leftClickCartesian = $this.getCatesian3FromPX(
        movement.position
      ));
      if (cartesian && cartesian.x) {
        if (positions.length == 0) {
          positions.push(cartesian.clone());
        }
        //以点为原点建立局部坐标系（东方向为x轴,北方向为y轴,垂直于地面为z轴），得到一个局部坐标到世界坐标转换的变换矩阵
        var localToWorld_Matrix =
          Cesium.Transforms.eastNorthUpToFixedFrame(cartesian);
        var dis = Cesium.Matrix4.multiplyByPoint(
          localToWorld_Matrix,
          Cesium.Cartesian3.fromElements(0, eventBus.height / 10, 0),
          new Cesium.Cartesian3()
        );
        drawLayer.entities.add({
          id: new Date().getTime().toString(),
          polyline: new Cesium.PolylineGraphics({
            positions: [cartesian, dis],
            width: options?.width || 3,
            material: new Cesium.PolylineDashMaterialProperty({
              color: Cesium.Color.fromCssColorString("#59FF9B"),
              dashLength: 20, //短划线长度
            }),
            clampToGround: options?.clampToGround || false,
          }),
        });
        angleLayer = new Cesium.Entity({
          position: cartesian,
          label: {
            text: "",
            show: true,
            showBackground: false,
            font: "18px  sans-serif",
            pixelOffset: new Cesium.Cartesian2(30, -20),
            // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0,500000),
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            disableDepthTestDistance: Number.POSITIVE_INFINITY, //防止模型被覆盖
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          },
        });
        drawLayer.entities.add(angleLayer);
        // 添加量测信息点
        positions.push(cartesian);
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // 鼠标移动监听
    handlers.setInputAction(function (movement: { endPosition: any }) {
      var cartesian = $this.getCatesian3FromPX(movement.endPosition);
      if (positions.length >= 2) {
        if (cartesian && cartesian.x) {
          positions.pop();
          positions.push(cartesian);
        }
      }
      const obj = {
        position: cartesian,
        text: "左键选点，右键结束",
      };
      tool.mouseTooltip(obj);
      if (leftClickCartesian) {
        let northAngle = tool.northIncludedAngle(leftClickCartesian, cartesian);
        angleLayer.label.text = new Cesium.CallbackProperty(function () {
          return northAngle[0] + " °";
        }, false);
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    handlers.setInputAction(function (movement: { position: any }) {
      handlers.destroy();
      handlers = null;
      let cartesian = $this.getCatesian3FromPX(movement.position);

      let obj = {
        id: ids,
        position: cartesian,
        entity: drawLayer,
      };
      tool.cesiumCursorConvert("default");
      tool.mouseTooltip({}, true);
      pointHadler.addMeasureInfoPoint(obj, true);
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    lineEntity.polyline.positions = new Cesium.CallbackProperty(function () {
      return positions;
    }, false);
    drawLayer.entities.add(lineEntity);
  }

  static cesiumCursorConvert(param: string) {
    let cesiumCss = document.getElementById("cesium-viewer");
    cesiumCss?.setAttribute("style", `cursor: ${param};`);
  }

  static leftDoubleClickSetting() {
    const viewer = window.viewer;
    viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
      Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
    );
  }

  static mouseTooltip(obj: any, destroy?: boolean) {
    const viewer = window.viewer;
    let entity = viewer.entities.getById("mouseMoveTooltipEntity");
    if (entity == undefined) {
      viewer.entities.add({
        id: "mouseMoveTooltipEntity",
        position: obj.position,
        label: {
          text: obj.text,
          font: "13px sans-serif",
          fillColor: Cesium.Color.WHITE,
          showBackground: false,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(-10, -10), // 偏移量
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
        },
      });
    } else if (destroy) {
      viewer.entities.removeById("mouseMoveTooltipEntity");
    } else {
      entity.position = new Cesium.CallbackProperty(function () {
        return obj.position;
      }, false);
    }
  }

  static distanceUnitConvert(param: string) {
    const count = Number(param);
    const distance =
      count > 10000
        ? (count / 1000).toFixed(3) + " km"
        : count.toFixed(2) + " m";
    return distance;
  }

  static computePositionArrayDistance(position: any[]) {
    const positionArray = tool.transformCartesianArrayToWGS84Array(position);
    const distance = tool.getPositionDistance(positionArray);
    const convert =
      Number(distance) == 0
        ? "0 m"
        : (Number(distance) / 1000).toFixed(4) + " km";
    return convert;
  }

  static northIncludedAngle(pointA: any, pointB: any) {
    if (pointA && pointB) {
      let distance = Cesium.Cartesian3.distance(pointA, pointB);

      //以a点为原点建立局部坐标系（东方向为x轴,北方向为y轴,垂直于地面为z轴），得到一个局部坐标到世界坐标转换的变换矩阵
      const localToWorld = Cesium.Transforms.eastNorthUpToFixedFrame(pointA);
      //求世界坐标到局部坐标的变换矩阵
      const worldToLocal = Cesium.Matrix4.inverse(
        localToWorld,
        new Cesium.Matrix4()
      );
      //A点在局部坐标的位置，其实就是局部坐标原点
      const localPosition_A = Cesium.Matrix4.multiplyByPoint(
        worldToLocal,
        pointA,
        new Cesium.Cartesian3()
      );
      //B点在以A点为原点的局部的坐标位置
      const localPosition_B = Cesium.Matrix4.multiplyByPoint(
        worldToLocal,
        pointB,
        new Cesium.Cartesian3()
      );
      //弧度
      const angle = Math.atan2(
        localPosition_B.x - localPosition_A.x,
        localPosition_B.y - localPosition_A.y
      );
      //角度
      let theta = angle * (180 / Math.PI);
      // if (theta < 0) {
      //   theta = theta + 360;
      // }

      return [theta.toFixed(1), tool.distanceUnitConvert(distance)];
    }
  }
  /**
   * @description: 将 SVG 转换为 Data URI
   * @param {string} svgCode  SVG 图标数据
   * @return {*}
   */
  static svgToDataUri(svgCode: string | number | boolean) {
    return "data:image/svg+xml;charset=utf-8," + encodeURIComponent(svgCode);
  }
}
export default tool;
