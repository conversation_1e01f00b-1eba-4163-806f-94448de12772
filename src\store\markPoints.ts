import { defineStore } from "pinia";
import { v4 as uuid4 } from "uuid";

// 定义周数据接口
export interface WeeklyDataItem {
  date: string;
  value: number;
}

// 定义Echarts数据接口
export interface EchartsData {
  pie?: {
    value: number;
    name: string;
  };
  bar?: number;
  line?: number[];
  scatter?: number[];
  radar?: number[];
  heatmap?: Array<number[]>; // [x轴索引, y轴索引, 值]
  gauge?: {
    value: number;
    name: string;
  };
  funnel?: Array<{
    value: number;
    name: string;
  }>;
}

// 定义显示层级接口
export interface DisplayLevel {
  min: number;
  max: number;
}

// 定义标记点数据接口
export interface MarkPoint {
  id: string;
  name: string;
  flag: string;
  position: {
    lon: number;
    lat: number;
    alt: number;
  };
  offset?: {
    x: number;
    y: number;
  };
  type: string;
  createTime: Date;
  description?: string;
  visible?: boolean; // 标记点是否可见
  weeklyData?: WeeklyDataItem[]; // 过去一周的数据
  echartsData?: EchartsData; // 适配Echarts的数据
  displayLevel?: DisplayLevel; // 显示层级范围
  isImported?: boolean; // 是否为导入的数据
  chartType?: string; // 图表类型：default, pie, bar
}

// 创建标记点存储
export const useMarkPointsStore = defineStore("markPoints", {
  state: () => {
    return {
      markPoints: [] as MarkPoint[],
    };
  },
  getters: {
    // 获取所有标记点
    getAllMarkPoints: (state) => state.markPoints,

    // 根据ID获取标记点
    getMarkPointById: (state) => (id: string) => {
      return state.markPoints.find((point) => point.id === id);
    },

    // 根据名称搜索标记点
    searchMarkPointsByName: (state) => (name: string) => {
      if (!name) return state.markPoints;
      return state.markPoints.filter((point) =>
        point.name.toLowerCase().includes(name.toLowerCase())
      );
    },
  },
  actions: {
    // 添加标记点
    addMarkPoint(markPoint: Partial<Omit<MarkPoint, "id">> & Pick<MarkPoint, "name" | "position" | "type">) {
      const newPoint: MarkPoint = {
        id: uuid4(),
        name: markPoint.name,
        position: markPoint.position,
        type: markPoint.type,
        flag: markPoint.flag || "", // 添加flag属性，默认为空字符串
        createTime: markPoint.createTime || new Date(),
        visible: markPoint.visible !== undefined ? markPoint.visible : false, // 默认不可见
        weeklyData: markPoint.weeklyData || [],
        echartsData: markPoint.echartsData || {},
        description: markPoint.description,
        offset: markPoint.offset,
        displayLevel: markPoint.displayLevel,
        isImported: markPoint.isImported || false, // 是否为导入的数据
        chartType: markPoint.chartType || "default", // 图表类型，默认为default
      };
      this.markPoints.push(newPoint);
      return newPoint;
    },

    // 更新标记点
    updateMarkPoint(id: string, data: Partial<MarkPoint>) {
      const index = this.markPoints.findIndex((point) => point.id === id);
      if (index !== -1) {
        this.markPoints[index] = { ...this.markPoints[index], ...data };
        return true;
      }
      return false;
    },

    // 删除标记点
    deleteMarkPoint(id: string) {
      const index = this.markPoints.findIndex((point) => point.id === id);
      if (index !== -1) {
        this.markPoints.splice(index, 1);
        return true;
      }
      return false;
    },

    // 清空所有标记点
    clearAllMarkPoints() {
      this.markPoints = [];
    },

  },
});
