@use './color.scss';
@use './elementPlus.scss';
:root {
  --gis-default-color: white;
}
.float-right{
  float: right;
}
.float-left{
  float: left;
}
.AJCenter{
  align-items: center;
  justify-content: center;
}
.display-flex {
  display: flex;
}
.text-center {
  text-align: center;
}
/* cursor 鼠标形状
------------------------------- */
// 默认
.cursor-default {
  cursor: default !important;
}
// 帮助
.cursor-help {
  cursor: help !important;
}
// 手指
.cursor-pointer {
  cursor: pointer !important;
}
// 移动
.cursor-move {
  cursor: move !important;
}
/* 字体大小全局样式
------------------------------- */
@for $i from 10 through 32 {
	.font#{$i} {
		font-size: #{$i}px !important;
	}
}
/* 外边距、内边距全局样式
------------------------------- */
@for $i from 1 through 200 {
  .mt#{$i} {
    margin-top: #{$i}px !important;
  }
  .mr#{$i} {
    margin-right: #{$i}px !important;
  }
  .mb#{$i} {
    margin-bottom: #{$i}px !important;
  }
  .ml#{$i} {
    margin-left: #{$i}px !important;
  }
  .pt#{$i} {
    padding-top: #{$i}px !important;
  }
  .pr#{$i} {
    padding-right: #{$i}px !important;
  }
  .pb#{$i} {
    padding-bottom: #{$i}px !important;
  }
  .pl#{$i} {
    padding-left: #{$i}px !important;
  }
  .h#{$i} {
    height: #{$i}px !important;
  }
  .lh#{$i} {
    line-height: #{$i}px !important;
  }
  .w#{$i} {
    height: #{$i}px !important;
  }
}
