<template>
  <!-- 轨迹组件本身不渲染任何 DOM，仅控制 Cesium 图层 -->
  <div class="hidden"></div>

  <div class="mainContainer">
    <div id="cesium-viewer" ref="viewerContainer"></div>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";

import { onMounted, onUnmounted, ref, watch } from "vue";

import { initMap } from "@/utils/initMap";

// 台风轨迹点类型
interface TyphoonPoint {
  lon: number;
  lat: number;
  time: string;
  level: string; // 强度等级
}

// // 组件 props
// const props = defineProps<{
//   track: TyphoonPoint[];
//   show?: boolean;
// }>();

const track = [
  { lon: 140.0, lat: 20.0, time: "2024-07-01 08:00", level: "热带风暴" },
  { lon: 142.5, lat: 22.0, time: "2024-07-01 14:00", level: "台风" },
  // ...
];

// const viewer = window.viewer as Cesium.Viewer; // 假设 viewer 已挂载到 window
let viewer: Cesium.Viewer;
const viewerContainer = ref();
onMounted(async () => {
  viewer = await initMap(viewerContainer);
  drawTrack();
});

let trackEntities: Cesium.Entity[] = [];

// 轨迹颜色映射
const levelColor: Record<string, Cesium.Color> = {
  强台风: Cesium.Color.RED,
  台风: Cesium.Color.ORANGE,
  热带风暴: Cesium.Color.YELLOW,
  热带低压: Cesium.Color.CYAN,
  超强台风: Cesium.Color.MAGENTA,
};

// 绘制轨迹
function drawTrack() {
  clearTrack();
  //   if (!props.show || !props.track?.length) return;

  // 轨迹线
  const positions = track.map((p) =>
    Cesium.Cartesian3.fromDegrees(p.lon, p.lat),
  );
  const polyline = viewer.entities.add({
    polyline: {
      positions,
      width: 4,
      material: new Cesium.PolylineGlowMaterialProperty({
        glowPower: 0.2,
        color: Cesium.Color.CYAN.withAlpha(0.7),
      }),
    },
  });
  trackEntities.push(polyline);

  // 轨迹点
  track.forEach((p, i) => {
    const color = levelColor[p.level] || Cesium.Color.WHITE;
    const entity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(p.lon, p.lat),
      point: {
        pixelSize: 12,
        color,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
      },
      label: {
        text: `${i + 1}`,
        font: "bold 16px sans-serif",
        fillColor: color,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        pixelOffset: new Cesium.Cartesian2(0, -18),
      },
      description: `时间: ${p.time}\n等级: ${p.level}`,
    });
    trackEntities.push(entity);
  });
}

// 清除轨迹
function clearTrack() {
  trackEntities.forEach((e) => viewer.entities.remove(e));
  trackEntities = [];
}

// onUnmounted(clearTrack);
// watch(() => [props.track, props.show], drawTrack, { deep: true });
</script>

<style scoped>
/* 可选：自定义样式，科技风可用 TailwindCSS 配合全局样式 */
</style>
