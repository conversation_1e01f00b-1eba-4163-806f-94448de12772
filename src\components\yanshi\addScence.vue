<template>
    <div id="AddScence">
        <div class="deploy">
            <div class="popMain">
                <div class="popover-title">
                    <span>兵力部署</span>
                    <!-- <span class="float-right red cursor-pointer" @click="close">
                        <i class="iconfont gis-shanchu"></i>
                    </span> -->
                </div>
                <div class="popContent">
                    <div class="iconBtn distance" @click="MarkModel.addModel('target')">
                        <i class="iconfont gis-icon_mubiao"></i>
                        <span class="font12 text-center mt8 color666">防护目标</span>
                    </div>
                    <div class="iconBtn area" @click="MarkModel.addModel('tank1')">
                        <i class="iconfont gis-tanke"></i>
                        <span class="font12 text-center mt8 color666">防护坦克</span>
                    </div>
                    <div class="iconBtn area" @click="MarkModel.addModel('TransportVehicle1')">
                        <i class="iconfont gis-daodanzhuangjiache"></i>
                        <span class="font12 text-center mt8 color666">反导装甲</span>
                    </div>
                    <div class="iconBtn distance" @click="MarkModel.addModel('warship')">
                        <i class="iconfont gis-junjian"></i>
                        <span class="font12 text-center mt8 color666">军舰</span>
                    </div>
                    <div class="iconBtn area" @click="MarkModel.addModel('warplane')">
                        <i class="iconfont gis-zhandouji"></i>
                        <span class="font12 text-center mt8 color666">无人机</span>
                    </div>
                    <div class="iconBtn area" @click="MarkModel.addModel('tank')">
                        <i class="iconfont gis-xiangxingfuhao-35-tanke"></i>
                        <span class="font12 text-center mt8 color666">机动-坦克</span>
                    </div>
                    <div class="iconBtn area" @click="MarkModel.addModel('TransportVehicle')">
                        <i class="iconfont gis-lunshizhuangjiache"></i>
                        <span class="font12 text-center mt8 color666">机动-装甲</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";
import "@/assets/iconfont/iconfont.css";
import "@/assets/iconfont/iconfont.js";
import MarkModel from "../yanshi/markModel.ts";

const close = () => {

}
</script>

<style lang="scss" scoped>
#AddScence {
    position: fixed;
    top: 44px;
    right: 70px;
}

.popMain {
    background-color: #e9e7e7;
    width: 260px;
    pointer-events: stroke;
    margin-top: 25px;
    border-radius: 6px;
    padding-bottom: 12px;

    .layer {
        margin: 6px;
        flex-direction: column;
        color: var(--gis-default-color);
        border: 2px solid transparent;

        span {
            margin: -20px 0 0 10px;
        }
    }

    .active_card {
        border: 2px solid #3b7cff;
        border-radius: 3px;
    }

    .popover-title {
        height: 40px;
        line-height: 40px;
        width: 90%;
        margin: auto;
    }

    .popContent {
        width: 90%;
        margin: auto;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-template-rows: 80px;
        column-gap: 20px;
        row-gap: 10px;
        margin-bottom: 10px;

        .iconBtn {
            display: flex;
            flex-direction: column;
            cursor: pointer;

            .iconfont {
                // font-size: 26px;
                font-size: 40px;
                background: #fff;
                align-items: center;
                justify-content: center;
                display: flex;
                border-radius: 8px;
                padding: 0;
                // padding: 7px 0;
            }

            &:hover,
            &:active {
                color: #3b7cff;
            }
        }
    }
}
</style>