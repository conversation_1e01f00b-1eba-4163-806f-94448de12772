<script setup lang="ts">
import MapMarker from "@/views/map/components/map-marker.vue";
import DeviceList from "@/components/device/deviceList.vue";
import RightClickMenu from "@/components/popup/rightClickMenu.vue";
import PointEditPopup from "@/components/popup/pointPopup.vue";
import PointSelectPopup from "@/components/popup/selectPointPopup.vue";
import modelInit from "@/components/yanshi/index.vue";
import { useCommonStore } from "~/src/store/common"

const commonStore = useCommonStore();

</script>

<template>
  <div class="layer_wrap" v-if="commonStore.getIsViewer">
    <MapMarker />
    <DeviceList />
    <RightClickMenu />
    <div class="pointPopup">
      <PointEditPopup ref="pointEditRef" />
      <PointSelectPopup ref="pointselectRef" />
      <modelInit ref="modelRef" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.pointPopup {
  top: 25px;
  left: 10px;
  position: fixed;
  display: grid;
  grid-template-rows: 1fr;
  max-height: calc(100% - 50px);
}
</style>
