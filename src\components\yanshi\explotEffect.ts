import * as Cesium from "cesium";

// explotEffect类用于在Cesium三维场景中创建和管理粒子特效相关的逻辑
class explotEffect {
    // 保存Cesium.Viewer实例，用于操作三维场景相关的功能，如添加图元、实体等
    private viewer: Cesium.Viewer;
    // 用于存储粒子特效的各种配置参数，例如发射速率、粒子生命周期、速度、大小、颜色等信息
    private viewModel: {
        emissionRate: number;
        gravity: number;
        minimumParticleLife: number;
        maximumParticleLife: number;
        minimumSpeed: number;
        maximumSpeed: number;
        startScale: number;
        endScale: number;
        particleSize: number;
        spin: boolean; // 设置默认值为false，增强明确性
        heading: number;
        pitch: number;
        roll: number;
    };
    // 用于表示粒子发射器的航向、俯仰、滚转相关信息的Cesium.HeadingPitchRoll对象
    private hpr: Cesium.HeadingPitchRoll| null = null;
    // 用于表示粒子发射器的平移、旋转、缩放相关信息的Cesium.TranslationRotationScale对象
    private trs: Cesium.TranslationRotationScale| null = null;
    // 用于存储粒子发射器在三维空间中的平移位置的Cesium.Cartesian3对象
    private translation: Cesium.Cartesian3 | null = null;
    // 用于存储粒子发射器的旋转信息（以四元数表示）的Cesium.Quaternion对象
    private rotation: Cesium.Quaternion | null = null;
    // 保存创建的粒子系统实例，用于后续对粒子特效的操作（如移除等），初始值为null
    private particleSystem: Cesium.ParticleSystem | null = null;
    // 用于存储粒子发射器的模型矩阵信息的Cesium.Matrix4对象，用于确定粒子发射器在世界坐标系中的位置和方向等
    private emitterModelMatrix: Cesium.Matrix4 | null = null;
    // 保存创建的Cesium.Entity实例，代表粒子特效在三维场景中的位置实体
    private entity: Cesium.Entity;

    // 构造函数，用于初始化粒子特效相关的基础属性和创建必要的Cesium对象
    constructor(viewer: Cesium.Viewer | undefined, obj: { lon: any; lat: any }) {
        if (!viewer) {
            throw new Error('必须传入有效的Cesium.Viewer实例');
        }
        this.viewer = viewer;
        this.viewModel = {
            emissionRate: 5,
            gravity: 0.0,
            minimumParticleLife: 1,
            maximumParticleLife: 6,
            minimumSpeed: 1.0,
            maximumSpeed: 4.0,
            startScale: 0.0,
            endScale: 80.0,
            particleSize: 550.0,
            spin: false,
            heading: 0,
            pitch: 0,
            roll: 0
        };
        // 初始化粒子发射器的模型矩阵对象，用于后续计算和设置粒子发射器在场景中的位置和方向相关信息
        this.emitterModelMatrix = new Cesium.Matrix4();
        // 初始化粒子发射器的平移位置对象，初始化为一个默认的Cesium.Cartesian3实例
        this.translation = new Cesium.Cartesian3();
        // 初始化粒子发射器的旋转信息对象（以四元数表示），初始化为一个默认的Cesium.Quaternion实例
        this.rotation = new Cesium.Quaternion();
        // 初始化粒子发射器的航向、俯仰、滚转相关信息对象，初始化为一个默认的Cesium.HeadingPitchRoll实例
        this.hpr = new Cesium.HeadingPitchRoll();
        // 初始化粒子发射器的平移、旋转、缩放相关信息对象，初始化为一个默认的Cesium.TranslationRotationScale实例
        this.trs = new Cesium.TranslationRotationScale();
        // 在指定的经纬度位置（通过传入的obj参数获取）添加一个Cesium.Entity实例，用于定位粒子特效在三维场景中的放置位置
        this.entity = this.viewer.entities.add({
            position: Cesium.Cartesian3.fromDegrees(obj.lon, obj.lat),
        });
        // 在构造函数中提前计算一些相对固定的属性值（如果适用），避免在后续频繁重复计算，提高性能
        this.initFixedProperties();
        // 创建粒子系统
        this.init();
    }

    // 用于提前计算一些在粒子特效生命周期内相对固定不变的属性值（如初始的旋转、平移等信息），避免重复计算
    private initFixedProperties() {
        // 例如，提前计算粒子发射器的初始航向、俯仰、滚转角度，这里设置为默认的0度，具体可根据实际需求调整
        this.hpr = Cesium.HeadingPitchRoll.fromDegrees(0.0, 0.0, 0.0);
        if (!this.trs) {
          throw new Error('必须初始化粒子发射器的平移、旋转、缩放相关信息对象');
        }
        // 计算并设置粒子发射器的初始平移位置，这里设置为特定的三维坐标值（-4.0, 0.0, 1.4），可按需修改
        this.trs.translation = Cesium.Cartesian3.fromElements(
            -4.0,
            0.0,
            1.4
        );
        // 根据已计算好的hpr（航向、俯仰、滚转角度）信息，计算并设置粒子发射器的初始旋转信息（以四元数表示）
        this.trs.rotation = Cesium.Quaternion.fromHeadingPitchRoll(
            this.hpr
        );
    }

    // 进行粒子特效的主要初始化操作，包括设置场景相关属性、创建粒子系统以及添加事件监听等
    init() {
        // 设置viewer的时钟属性，使其开始动画，让场景中的元素（如粒子系统）能够动起来
        this.viewer.clock.shouldAnimate = true;
        // 关闭地形深度检测，针对粒子特效相关的场景渲染需求，避免粒子与地形之间不必要的深度交互影响渲染效果
        this.viewer.scene.globe.depthTestAgainstTerrain = false;
        // 创建粒子系统实例，并添加到viewer的场景图元集合中，配置了粒子的各种属性，如图片、颜色、尺寸、发射速率等
        const particleSystem = this.viewer.scene.primitives.add(
            new Cesium.ParticleSystem({
                image: "src/assets/images/explot.png",
                startColor: Cesium.Color.RED.withAlpha(0.7),
                endColor: Cesium.Color.YELLOW.withAlpha(0.3),
                startScale: this.viewModel.startScale,
                endScale: this.viewModel.endScale,
                minimumParticleLife: this.viewModel.minimumParticleLife,
                maximumParticleLife: this.viewModel.maximumParticleLife,
                minimumSpeed: this.viewModel.minimumSpeed,
                maximumSpeed: this.viewModel.maximumSpeed,
                imageSize: new Cesium.Cartesian2(
                    this.viewModel.particleSize,
                    this.viewModel.particleSize
                ),
                emissionRate: this.viewModel.emissionRate,
                lifetime: 16.0,
                sizeInMeters: true,
                emitter: new Cesium.CircleEmitter(5.0),
            })
        );
        // 将创建好的粒子系统实例保存到类的particleSystem属性中，方便后续对粒子系统进行操作
        this.particleSystem = particleSystem;
        // 调用preUpdateEvent方法，添加场景渲染前的更新事件监听，用于在每次渲染前更新粒子系统相关的状态信息
        this.preUpdateEvent();
    }

    // 使用箭头函数绑定this，避免this指向问题，用于监听viewer.scene.preUpdate事件，在每次场景渲染前更新粒子系统的相关矩阵信息以及处理粒子发射器的旋转逻辑（如果spin属性为true）
    private preUpdateEvent = () => {
        this.viewer.scene.preUpdate.addEventListener((scene, time) => {
            if (!this.particleSystem) {
                console.error('粒子系统实例为null，可能出现异常');
                return;
            }
            this.particleSystem.modelMatrix = this.computeModelMatrix(this.entity, time);
            if (this.viewModel.spin) {
                this.viewModel.heading += 1.0;
                this.viewModel.pitch += 1.0;
                this.viewModel.roll += 1.0;
            }
        });
    };

    // 计算粒子系统的整体模型矩阵，根据给定的实体（entity）和时间（time）信息，返回对应的Cesium.Matrix4模型矩阵对象
    private computeModelMatrix(entity: Cesium.Entity, time: Cesium.JulianDate): Cesium.Matrix4 {
        return entity.computeModelMatrix(time, new Cesium.Matrix4());
    }

    // 用于移除之前添加的场景渲染前更新事件监听器，并清理相关的属性对象，释放内存，避免内存泄漏
    private removeEvent() {
        this.viewer.scene.preUpdate.removeEventListener(this.preUpdateEvent, this);
        
        if (this.emitterModelMatrix) {
          this.emitterModelMatrix = null;
        }
        if (this.translation) {
           this.translation = null;
        }
        if (this.rotation) {
           this.rotation = null;
        }
        if (this.hpr) {
           this.hpr = null;
        }
        if (this.trs) {
           this.trs = null;
        }
    }

    // 对外提供的用于彻底移除粒子特效的方法，先调用removeEvent方法清除事件监听，然后从场景的图元集合中移除粒子系统实例，以及从实体集合中移除对应的entity，完成整个粒子特效相关资源的清理
    remove() {
        console.log("移除了。。。。。");
        this.removeEvent();
        if (this.particleSystem) {
            // 从viewer的场景图元集合中移除已创建的粒子系统实例，释放相关资源
            this.viewer.scene.primitives.remove(this.particleSystem);
        }
        // 从viewer的实体集合中移除代表粒子特效位置的entity实例，释放相关资源
        this.viewer.entities.remove(this.entity);
    }
}

export default explotEffect;