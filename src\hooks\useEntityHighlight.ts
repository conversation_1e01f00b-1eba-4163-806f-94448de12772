import * as Cesium from 'cesium';

/**
 * 实体高亮效果配置选项
 */
export interface HighlightOptions {
  /** 动画循环次数，默认为5 */
  maxAnimationCount?: number;
  /** 动画间隔时间(ms)，默认为50 */
  interval?: number;
  /** 最大缩放比例，相对于原始大小，默认为1.5 */
  maxScale?: number;
  /** 颜色亮度增强系数，默认为1.5 */
  colorBrightness?: number;
}

/**
 * 实体高亮效果钩子
 * 提供对Cesium实体的高亮动画效果
 */
export function useEntityHighlight() {
  /**
   * 高亮显示实体
   * @param entity Cesium实体对象
   * @param options 高亮效果配置选项
   */
  const highlightEntity = (entity: Cesium.Entity, options?: HighlightOptions) => {
    if (!entity || !entity.billboard) return;

    // 配置选项
    const maxAnimationCount = options?.maxAnimationCount ?? 1;
    const interval = options?.interval ?? 50;
    const maxScale = options?.maxScale ?? 1.5;
    const colorBrightness = options?.colorBrightness ?? 1.5;

    // 保存原始比例和颜色
    const originalScale = entity.billboard?.scale?.getValue?.(Cesium.JulianDate.now()) || 1.0;
    const originalColor = entity.billboard?.color?.getValue?.(Cesium.JulianDate.now()) || new Cesium.Color(1, 1, 1, 1);

    // 创建高亮动画效果
    let highlightAnimation: number | null = null;
    let scaleDirection = 1;
    let currentScale = originalScale;
    let animationCount = 0;

    // 清除之前可能存在的动画
    if ((entity as any)._highlightAnimation) {
      clearInterval((entity as any)._highlightAnimation);
    }

    // 创建闪烁动画
    highlightAnimation = window.setInterval(() => {
      // 更新比例
      currentScale += 0.05 * scaleDirection;

      // 在原始大小和最大缩放比例之间变化
      if (currentScale >= originalScale * maxScale) {
        scaleDirection = -1;
        animationCount++;
      } else if (currentScale <= originalScale) {
        scaleDirection = 1;
      }

      // 应用新的比例
      if (entity.billboard) {
        entity.billboard.scale = new Cesium.ConstantProperty(currentScale);

        // 交替使用原始颜色和高亮颜色
        const phase = Math.floor(animationCount / 2) % 2;
        if (phase === 0) {
          // 高亮颜色 - 更亮的版本
          const highlightColor = new Cesium.Color(
            Math.min(originalColor.red * colorBrightness, 1),
            Math.min(originalColor.green * colorBrightness, 1),
            Math.min(originalColor.blue * colorBrightness, 1),
            1
          );
          entity.billboard.color = new Cesium.ConstantProperty(highlightColor);
        } else {
          // 恢复原始颜色
          entity.billboard.color = new Cesium.ConstantProperty(originalColor);
        }
      }

      // 达到最大动画次数后停止
      if (animationCount >= maxAnimationCount) {
        clearInterval(highlightAnimation!);

        // 恢复原始状态
        if (entity.billboard) {
          entity.billboard.scale = new Cesium.ConstantProperty(originalScale);
          entity.billboard.color = new Cesium.ConstantProperty(originalColor);
        }

        // 清除引用
        (entity as any)._highlightAnimation = null;
      }
    }, interval);

    // 保存动画引用以便后续清除
    (entity as any)._highlightAnimation = highlightAnimation;
  };

  /**
   * 停止实体的高亮效果
   * @param entity Cesium实体对象
   */
  const stopHighlight = (entity: Cesium.Entity) => {
    if (!entity || !(entity as any)._highlightAnimation) return;

    clearInterval((entity as any)._highlightAnimation);

    // 恢复原始状态
    if (entity.billboard) {
      const originalScale = entity.billboard?.scale?.getValue?.(Cesium.JulianDate.now()) || 1.0;
      const originalColor = entity.billboard?.color?.getValue?.(Cesium.JulianDate.now()) || new Cesium.Color(1, 1, 1, 1);

      entity.billboard.scale = new Cesium.ConstantProperty(originalScale);
      entity.billboard.color = new Cesium.ConstantProperty(originalColor);
    }

    // 清除引用
    (entity as any)._highlightAnimation = null;
  };

  return {
    highlightEntity,
    stopHighlight
  };
}

export default useEntityHighlight;
