/**
 * 北京地区预设标记点数据（带偏移量和过去一周的时间序列数据）
 * 添加了层级显示控制功能
 */
import { MarkerType } from "@/constants/markerTypes";
import echartsData from "./echartsData";

// 定义不同层级的显示范围（单位：米）
export const DisplayLevels = {
  // 近距离层级（0-5000米）- 显示所有标记点
  NEAR: {
    min: 0,
    max: 5000
  },
  // 中距离层级（0-50000米）- 显示所有标记点
  MEDIUM: {
    min: 0,
    max: 50000
  },
  // 远距离层级（0-3000000米）- 只显示最重要的标记点（地标和交通设施）
  FAR: {
    min: 0,
    max: 3000000
  }
};

// 生成随机偏移量的辅助函数
const generateRandomOffset = () => {
  return {
    x: parseFloat((Math.random() * 3 - 1.5).toFixed(2)),  // -1.5 到 1.5 之间的随机数
    y: parseFloat((Math.random() * 3 - 1.5).toFixed(2)),  // -1.5 到 1.5 之间的随机数
    z: parseFloat((Math.random() * 2).toFixed(2))         // 0 到 2 之间的随机数
  };
};

// 生成过去一周的日期数组
const generatePastWeekDates = () => {
  const dates = [];
  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    dates.push(date.toISOString().split('T')[0]); // 格式化为 YYYY-MM-DD
  }
  return dates;
};

// 生成过去一周的随机数据
const generatePastWeekData = (type: MarkerType) => {
  const dates = generatePastWeekDates();
  const baseValue = Math.floor(Math.random() * 50) + 10; // 基础值：10-60之间的随机数

  // 根据不同类型设置不同的数据波动范围
  let fluctuation = 10; // 默认波动范围
  switch (type) {
    case MarkerType.LANDMARK:
      fluctuation = 20; // 地标波动较大
      break;
    case MarkerType.TRANSPORTATION:
      fluctuation = 25; // 交通波动最大
      break;
    case MarkerType.SHOPPING:
      fluctuation = 15; // 购物波动中等
      break;
    default:
      fluctuation = 10; // 其他类型波动较小
  }

  // 生成每天的数据
  return dates.map(date => {
    // 生成-fluctuation到+fluctuation之间的随机波动
    const dailyFluctuation = Math.floor(Math.random() * fluctuation * 2) - fluctuation;
    // 确保值不小于0
    const value = Math.max(0, baseValue + dailyFluctuation);
    return {
      date,
      value
    };
  });
};

// 原始标记点数据
const originalMarkers = [
  {
    name: '台北101',
    flag: 'tw',
    position: {
      lon: 121.5645,
      lat: 25.033,
      alt: 0,
    },
    type: MarkerType.LANDMARK,
    description: '台湾著名地标之一，曾是世界最高楼，象征现代化与经济发展',
  },
  {
    name: '天安门广场',
    flag: 'cn',
    position: {
      lon: 116.397452,
      lat: 39.909187,
      alt: 0,
    },
    type: MarkerType.LANDMARK,
    description: '中国政治和文化的象征，世界上最大的城市广场之一',
  },
  {
    name: '故宫博物院',
    position: {
      lon: 116.403414,
      lat: 39.917513,
      alt: 0,
    },
    type: MarkerType.LANDMARK,
    description:
      '中国明清两代的皇家宫殿，世界上现存规模最大、保存最为完整的木质结构古建筑之一',
  },
  {
    name: '颐和园',
    position: {
      lon: 116.2755,
      lat: 39.9988,
      alt: 0,
    },
    type: MarkerType.PARK,
    description:
      '中国清朝时期的皇家园林，以昆明湖和万寿山为基址，以杭州西湖为蓝本',
  },
  {
    name: '北京大学',
    position: {
      lon: 116.3046,
      lat: 39.9869,
      alt: 0,
    },
    type: MarkerType.EDUCATION,
    description: '中国最著名的高等学府之一，创建于1898年',
  },
  {
    name: '清华大学',
    position: {
      lon: 116.3252,
      lat: 40.0003,
      alt: 0,
    },
    type: MarkerType.EDUCATION,
    description: '中国著名的高等学府，创建于1911年',
  },
  {
    name: '鸟巢(国家体育场)',
    position: {
      lon: 116.3901,
      lat: 39.9926,
      alt: 0,
    },
    type: MarkerType.SPORTS,
    description: '2008年北京奥运会的主体育场，因其外观酷似鸟巢而得名',
  },
  {
    name: '水立方(国家游泳中心)',
    position: {
      lon: 116.3874,
      lat: 39.9925,
      alt: 0,
    },
    type: MarkerType.SPORTS,
    description: '2008年北京奥运会游泳比赛场馆，外形似水立方',
  },
  {
    name: '798艺术区',
    position: {
      lon: 116.4954,
      lat: 39.9841,
      alt: 0,
    },
    type: MarkerType.CULTURE,
    description: '位于北京朝阳区的艺术区，由废弃的军工厂改建而成',
  },
  {
    name: '北京首都国际机场',
    position: {
      lon: 116.5927,
      lat: 40.0799,
      alt: 0,
    },
    type: MarkerType.TRANSPORTATION,
    description: '中国最大的民用航空港之一，位于北京市朝阳区',
  },
  {
    name: '北京大兴国际机场',
    position: {
      lon: 116.4097,
      lat: 39.5098,
      alt: 0,
    },
    type: MarkerType.TRANSPORTATION,
    description: '北京的第二座国际机场，于2019年9月投入使用',
  },
  {
    name: '北京西站',
    position: {
      lon: 116.3212,
      lat: 39.8954,
      alt: 0,
    },
    type: MarkerType.TRANSPORTATION,
    description: '中国铁路北京局集团有限公司管辖的特等站，位于北京市丰台区',
  },
  {
    name: '北京南站',
    position: {
      lon: 116.3783,
      lat: 39.8651,
      alt: 0,
    },
    type: MarkerType.TRANSPORTATION,
    description:
      '中国铁路北京局集团有限公司管辖的特等站，是京沪高速铁路的北端起点站',
  },
  {
    name: '王府井步行街',
    position: {
      lon: 116.4177,
      lat: 39.9149,
      alt: 0,
    },
    type: MarkerType.SHOPPING,
    description: '北京市最著名的商业街之一，有着百年历史',
  },
  {
    name: '三里屯太古里',
    position: {
      lon: 116.4546,
      lat: 39.9384,
      alt: 0,
    },
    type: MarkerType.SHOPPING,
    description: '位于北京市朝阳区的开放式购物中心，是北京时尚地标',
  },
  {
    name: '北京动物园',
    position: {
      lon: 116.3387,
      lat: 39.9403,
      alt: 0,
    },
    type: MarkerType.PARK,
    description: '中国历史最悠久的动物园之一，以收藏中国特产珍稀动物为特色',
  },
  {
    name: '北海公园',
    position: {
      lon: 116.3845,
      lat: 39.9263,
      alt: 0,
    },
    type: MarkerType.PARK,
    description: '中国现存最古老、最完整、最具代表性的皇家园林之一',
  },
  {
    name: '中国国家博物馆',
    position: {
      lon: 116.401,
      lat: 39.9053,
      alt: 0,
    },
    type: MarkerType.CULTURE,
    description: '位于北京市中心天安门广场东侧，是中国最大的博物馆',
  },
  {
    name: '中央电视台总部大楼',
    position: {
      lon: 116.4655,
      lat: 39.9157,
      alt: 0,
    },
    type: MarkerType.LANDMARK,
    description: "由荷兰建筑师库哈斯设计，因其独特的形状被称为'大裤衩'",
  },
  {
    name: '奥林匹克公园',
    position: {
      lon: 116.3903,
      lat: 40.0003,
      alt: 0,
    },
    type: MarkerType.PARK,
    description: '2008年北京奥运会的主会场所在地，包括鸟巢、水立方等标志性建筑',
  },
  {
    name: '北京天文馆',
    position: {
      lon: 116.3459,
      lat: 39.9427,
      alt: 0,
    },
    type: MarkerType.EDUCATION,
    description: '中国规模最大的天文科普馆，建于1957年',
  },
];

/**
 * 根据标记点类型确定显示层级
 * @param type 标记点类型
 * @returns 显示层级对象
 */
const getDisplayLevelByType = (type: MarkerType) => {
  // 根据标记点类型确定显示层级
  switch (type) {
    case MarkerType.LANDMARK:
    case MarkerType.TRANSPORTATION:
      // 地标和交通设施在远距离也可见
      return DisplayLevels.FAR;

    // 所有标记点在中距离都可见，不再区分类型
    // 这里不再有中距离的特殊处理，因为所有点都会显示在中距离

    default:
      // 所有其他类型的标记点在中距离可见
      return DisplayLevels.MEDIUM;
  }
};

// 为每个标记点添加随机偏移量、创建时间、过去一周的数据和显示层级
export const beijingMarkers = originalMarkers.map(marker => {
  // 生成随机的创建时间（过去30天内）
  const createTime = new Date();
  createTime.setDate(createTime.getDate() - Math.floor(Math.random() * 30));

  // 获取显示层级
  const displayLevel = getDisplayLevelByType(marker.type);

  return {
    ...marker,
    offset: generateRandomOffset(),
    createTime,
    // 添加过去一周的数据
    weeklyData: generatePastWeekData(marker.type),
    // 添加适配Echarts的数据格式
    echartsData,
    // 添加显示层级
    displayLevel
  };
});
