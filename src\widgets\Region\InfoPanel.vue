<template>
  <transition name="fade">
    <div
      v-if="visible && name"
      class="info-panel fixed top-4 left-4 px-6 py-3 text-sm text-cyan-400 font-mono bg-black/70 backdrop-blur-md border border-cyan-500 rounded-lg shadow-lg overflow-hidden z-10"
    >
      <div class="flex items-center">
        <Location class="size-4 mr-1 text-cyan-500 animate-bounce" />
        <span class="font-semibold text-sm">{{ name }}</span>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { defineProps, ref, watch } from "vue";

import { Location } from "@element-plus/icons-vue";

const props = defineProps({
  name: {
    type: String,
    default: "",
  },
});

const visible = ref(true);
watch(
  () => props.name,
  () => {
    if (props.name) visible.value = true;
  },
);
</script>

<style scoped>
.info-panel::before,
.info-panel::after {
  content: "";
  position: absolute;
  border: 1px solid #06b6d4;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: -1;
  animation: border-glow 3s linear infinite;
  pointer-events: none;
}

.info-panel::after {
  filter: blur(4px);
  opacity: 0.7;
}

.info-panel::before {
  clip-path: polygon(0% 0%, 10% 0%, 10% 5%, 5% 5%, 5% 10%, 0% 10%, 0% 0%);
}

.info-panel::after {
  clip-path: polygon(
    100% 100%,
    90% 100%,
    90% 95%,
    95% 95%,
    95% 90%,
    100% 90%,
    100% 100%
  );
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.4s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
