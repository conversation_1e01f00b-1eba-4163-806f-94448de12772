<!-- 模型拾取高亮 -->
<template>
  <div class="mainContainer">
    <div id="cesium-viewer" ref="viewerContainer"></div>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";

import { onBeforeUnmount, onMounted, ref } from "vue";

import { createModel } from "@/utils/createModel";
import { initMap } from "@/utils/initMap";

const viewerContainer = ref<HTMLElement | null>(null);
let viewer: Cesium.Viewer | null = null;
const buildingURL = "/src/assets/models/building/building.glb";
// const buildingTileURL = "/src/assets/models/building/tileset.json";

let animationInterval: number | null = null;

let cameraUpdateListener: Cesium.Event.RemoveCallback | null = null;

const initViewer = async () => {
  viewer = await initMap(viewerContainer);
  viewer.entities.removeAll();
  // inspector mode
  viewer.extend(Cesium.viewerCesiumInspectorMixin);

  const startLon = 116.3975; // 天安门
  const startLat = 39.9087;

  const entity = createModel(
    viewer,
    buildingURL,
    0,
    startLon,
    startLat,
    0,
    90,
    0,
    0.5,
  );

  viewer.trackedEntity = entity;
  // const tileset = await Cesium.Cesium3DTileset.fromUrl(buildingTileURL);
  // viewer.scene.primitives.add(tileset);
  // viewer.zoomTo(tileset);
};

onMounted(() => {
  initViewer();
});

onBeforeUnmount(() => {
  if (cameraUpdateListener) {
  }
  if (viewer) {
    viewer.destroy();
  }
  if (animationInterval) {
    clearInterval(animationInterval);
  }
});
</script>

<style scoped>
.mainContainer {
  position: relative;
  width: 100%;
  height: 100vh;
}

#cesium-viewer {
  width: 100%;
  height: 100%;
}
</style>
