import * as Cesium from "cesium";

class DegreesCovert {
  /**
   * @description: 经纬度转度分秒格式
   * @param {any} param
   * @return {*} 返回 "度 分 秒" 格式
   */
  static DegreesCoverttoDuFenMiao(param: any) {
    const degree = parseInt(param);
    const minute = (param % degree) * 60;
    const second = (minute % Math.floor(minute)) * 60;
    return degree + "° " + Math.floor(minute) + "′ " + second.toFixed(2) + "″";
  }
}
export default DegreesCovert;
