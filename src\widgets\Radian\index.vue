<!-- 模板 -->
<template>
  <div class="mainContainer">
    <div id="cesium-viewer" ref="viewerContainer"></div>
    <div class="fixed top-0 left-0">
      <button @click="render">渲染</button>
      <button @click="onClear">清除</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";

import { onBeforeUnmount, onMounted, ref } from "vue";

import { initMap } from "@/utils/initMap";

import Radiant from "./radiant";

const viewerContainer = ref<HTMLElement | null>(null);
let viewer: Cesium.Viewer | null = null;
let circleWave: Radiant | null = null;
const location = [114.31, 30.57, 0];
const radiantId = "cirCleWave1";

const initViewer = async () => {
  viewer = await initMap(viewerContainer);
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(...location.slice(0, 2), 20000),
    // orientation: {
    //   heading: 0,
    //   pitch: 0,
    //   roll: 0,
    // },
  });
  circleWave = new Radiant(viewer, radiantId);
  render();
};

const render = () => {
  circleWave?.add(location, "blue", 1000, 3000);
};

const onClear = () => {
  circleWave?.del();
};

onMounted(() => {
  initViewer();
});

// onBeforeUnmount(() => {
//   if (viewer) {
//     onClear();
//     viewer.destroy();
//   }
// });
</script>
