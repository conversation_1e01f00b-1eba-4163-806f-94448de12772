import { defineStore } from "pinia";
// useStore 可以是 useUser、useCart 之类的任何东西
// 第一个参数是应用程序中 store 的唯一 id
export const ModelPath = defineStore("path", {
  // other options...
  state: () => {
    return {
        target:[], //防护目标数组
        definedEquipment:[], //防御设备（静止设备·········）
        moveEquipment:[], //动态模型设备数组
        hitEquipment:[] ,//动态打击模型数组
        id: 0,
        demoData:[],


    }
  },
});
