/* elementplus-dark-theme.scss */

/* 基础色彩配置 */
:root {
  --el-color-primary: #00eaff;
  --el-color-success: #30c87c;
  --el-color-warning: #ffc107;
  --el-color-danger: #ff5f5f;
  --el-color-error: #ff5f5f;
  --el-color-info: #5c7f99;

  --el-bg-color: #0f1f2c;
  --el-bg-color-overlay: rgba(15, 31, 44, 0.85);

  --el-text-color-primary: #ffffff;
  --el-text-color-regular: #a8b3c1;
  --el-text-color-secondary: #708090;
  --el-text-color-disabled: #4a5b6a;

  --el-border-color: #2b3e50;
  --el-border-color-light: #3d4f63;
  --el-border-color-lighter: #4e6279;

  --el-border-radius-base: 0px;
  --el-fill-color-light: rgba(255, 255, 255, 0.05);
  --el-fill-color-dark: rgba(0, 0, 0, 0.3);
}

/* 通用字体 */
body {
  font-family: '<PERSON><PERSON><PERSON>', 'DIN', 'Helvetica Neue', sans-serif;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);
}

/* 按钮 */
.el-button {
  border: 1px solid var(--el-color-primary);
  color: var(--el-color-primary);
  background-color: transparent;
  text-shadow: 0 0 3px var(--el-color-primary);
  box-shadow: 0 0 5px var(--el-color-primary) inset, 0 0 5px var(--el-color-primary);
  border-radius: 0;
}

.el-button:hover {
  background-color: var(--el-color-primary);
  color: #000;
}

/* 卡片、弹窗、表单区域 */
.el-card,
.el-dialog,
.el-drawer {
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color);
  box-shadow: 0 0 10px rgba(0, 234, 255, 0.2);
  border-radius: 0;
}

/* 输入框 */
.el-input__inner {
  background-color: var(--el-fill-color-light);
  color: var(--el-text-color-primary);
  border: 1px solid var(--el-border-color-light);
  border-radius: 0;
  box-shadow: 0 0 4px rgba(0, 234, 255, 0.2);
  transition: all 0.3s;
}

.el-input__inner:focus {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 8px var(--el-color-primary);
}

.el-input.is-disabled .el-input__inner {
  background-color: var(--el-fill-color-dark);
  color: var(--el-text-color-disabled);
  border-color: var(--el-border-color);
  box-shadow: none;
}

/* 表格 */
.el-table {
  background-color: transparent;
  color: var(--el-text-color-primary);
}

.el-table th,
.el-table td {
  background-color: var(--el-bg-color-overlay);
  border-color: var(--el-border-color-light);
}

/* 滑块 */
.el-slider__bar {
  background-color: var(--el-color-primary);
  box-shadow: 0 0 5px var(--el-color-primary);
}

/* 进度条 */
.el-progress-bar__inner {
  background-color: var(--el-color-primary);
}

/* 标签页 */
.el-tabs__item {
  color: var(--el-text-color-regular);
}

.el-tabs__item.is-active {
  color: var(--el-color-primary);
  border-bottom: 2px solid var(--el-color-primary);
}

/* 消息提示 */
.el-message-box,
.el-notification {
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  color: var(--el-text-color-primary);
  box-shadow: 0 0 10px rgba(0, 234, 255, 0.2);
}

/* 弹出菜单 */
.el-dropdown-menu,
.el-menu {
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color);
  color: var(--el-text-color-primary);
}

/* 日期选择器 */
.el-date-picker {
  background-color: var(--el-bg-color-overlay);
  color: var(--el-text-color-primary);
}

/* Switch 开关 */
.el-switch__core {
  background-color: var(--el-border-color);
}

.el-switch.is-checked .el-switch__core {
  background-color: var(--el-color-primary);
  box-shadow: 0 0 5px var(--el-color-primary);
}

/* 加载 spinner */
.el-loading-spinner .circular {
  color: var(--el-color-primary);
}

/* Tooltip */
.el-tooltip__popper {
  background-color: var(--el-bg-color-overlay);
  color: var(--el-text-color-primary);
  border: 1px solid var(--el-border-color-light);
}