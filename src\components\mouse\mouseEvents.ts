import * as Cesium from "cesium";

import bus from "@/utils/bus";

import tool from "../tool/tool";

class mouseEvent {
  //监听鼠标移动和滚轮滚动
  static mouseMove(_viewer?: Cesium.Viewer) {
    const viewer = _viewer || window.viewer;
    // 绑定屏幕空间事件
    let handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    var ellipsoid = viewer.scene.globe.ellipsoid;
    //监听鼠标移动
    handler.setInputAction(function (event: any) {
      // 检查鼠标是否悬浮在标记点上
      const pickedFeature = viewer.scene.pick(event.endPosition);
      if (Cesium.defined(pickedFeature) && pickedFeature.id) {
        const entity = pickedFeature.id;
        // 检查是否是标记点
        if (
          entity.properties &&
          entity.properties.isBillboard &&
          entity.properties.isBillboard._value === true
        ) {
          // 鼠标悬浮在标记点上，显示小手光标
          tool.cesiumCursorConvert("pointer");
        } else {
          // 鼠标不在标记点上，恢复默认光标
          tool.cesiumCursorConvert("default");
        }
      } else {
        // 鼠标不在任何实体上，恢复默认光标
        tool.cesiumCursorConvert("default");
      }

      var cartesian = viewer.camera.pickEllipsoid(event.endPosition, ellipsoid);
      if (cartesian) {
        //将地图坐标（弧度）转为十进制的度数
        let cartographic =
          viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian);
        var lon = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6); //经
        let lat = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6); //纬
        let height = viewer.camera.positionCartographic.height; //高
        bus.emit("mouseMove", {
          lon: lon,
          lat: lat,
          height: height,
          handler: handler,
        });
        // console.log(viewer.scene.camera.pickEllipsoid(new Cesium.Cartesian2(viewer.canvas.clientWidth / 2, viewer.canvas.clientHeight / 2))) //当前视角中心点
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    handler.setInputAction((_event: any) => {
      let tilesToRender = viewer.scene.globe._surface._tilesToRender;
      if (Cesium.defined(tilesToRender)) {
        let height = viewer.camera.positionCartographic.height; //高
        // 获取当前地图瓦片级别
        bus.emit("mouseWheel", {
          level: tilesToRender[0].level,
          height: height,
          handler: handler,
        });
      }
    }, Cesium.ScreenSpaceEventType.WHEEL);
  }
  //点击实体类
  static clickEntity() {
    const viewer = window.viewer;
    let handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    handler.setInputAction(function (movement: {
      position: Cesium.Cartesian2;
    }) {
      var pickedObject = viewer.scene.pick(movement.position);
      if (
        Cesium.defined(pickedObject) &&
        pickedObject.id instanceof Cesium.Entity
      ) {
        // console.log("点击=>", pickedObject.id);
        var entity = pickedObject.id;
        //点击cameraPoint类
        if (entity.properties?.type._value == "cameraPoint") {
          mouseEvent.nearbyEntity(entity.position._value);
        } else if (entity.properties?.type._value == "closeDataSource") {
          //删除单个测距测面积
          const array = viewer.dataSources._dataSources;
          array.forEach((el: Cesium.DataSource) => {
            if (el.name == entity.id) {
              viewer.dataSources.remove(el);
            }
          });
        }
      } else if (
        Cesium.defined(pickedObject) &&
        pickedObject.primitive instanceof Cesium.Primitive
      ) {
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // 右键点击实体弹出右键菜单
    handler.setInputAction(function (movement: {
      position: Cesium.Cartesian2;
    }) {
      var pickedObject = viewer.scene.pick(movement.position);
      if (
        Cesium.defined(pickedObject) &&
        pickedObject.id instanceof Cesium.Entity
      ) {
        console.log("点击=>", pickedObject.id);
        bus.emit("editEntity", {
          entity: pickedObject.id,
          cartesian2: movement.position,
        });
      }
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }
  /**
   * @description: 控制全局监听点击实体类事件
   * @param {boolean} flag  true：开启监听 false：关闭监听
   * @param {any} handler 监听属性值（关闭监听时必传）
   * @return {*} 无返回值
   */
  static controlClickEntity() {
    const viewer = window.viewer;
    let handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    handler.setInputAction(function () {
      console.log("左键按下.....");
      handler.setInputAction(function () {
        console.log("按下并移动.....");
        handler.destroy();
        return true;
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
  }
  /**
   * @description: 点击实体查询附近范围内相同类型实体类
   * @param {Cesium} position 点位
   * @param {number} range 范围（默认500米）
   * @return {*}
   */
  static nearbyEntity(position: Cesium.Cartesian3, range?: number) {
    const viewer = window.viewer,
      list: any = [];
    const entities = viewer.entities.values;
    entities.forEach((entity: any) => {
      var distance = Cesium.Cartesian3.distance(
        position,
        entity.position._value,
      );
      if (distance <= (range || 500)) {
        list.push({ name: entity.name, id: entity.id });
        // console.log("距离=》", distance <= range);
      }
      // console.log("数组=》", list);
    });
    bus.emit("cameraList", list);
  }
  //   static eventListener_MouseRightClick = (handler:any,globalViewer:any) => {
  //     const viewer: Cesium.Viewer = window.viewer;
  //     handler.setInputAction(function (evt:any) {
  //         //设置监听方法
  //         let scene = globalViewer.scene; //cesium全局对象
  //         let pick = scene.pick(evt.position);
  //         // cartesian = scene.pickPosition(evt.position);    // 获取鼠标位置
  //         // cameraHeight = Math.ceil(viewer.camera.positionCartographic.height);     // 获取相机高度
  //         if (pick == undefined) {
  //             console.log("空白处右键");
  //         } else {
  //             console.log("实体处右键",pick);
  //             // 菜单位置 ----------------- store里面参数如下
  //             store.showMeau = true
  //             store.rightClickMeauData = {
  //                 top:evt.position.y + 'px',
  //                 left:evt.position.x + 'px'
  //             }
  //             //do somethine
  //         }

  //     }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  // }
  static mouseRightClickStop(event: any) {
    // if (viewer){
    // event.setInputAction(function (movement: { position: Cesium.Cartesian2; }) {
    //     var cartesian = tool.getCatesian3FromPX(movement.position);
    //     console.log("监听左键",cartesian);
    //   }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // }
  }
}
export default mouseEvent;
