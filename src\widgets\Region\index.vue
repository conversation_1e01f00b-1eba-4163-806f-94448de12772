<!-- 区域悬浮高亮 -->
<script setup lang="ts">
import * as Cesium from "cesium";

import { onMounted, onUnmounted, ref } from "vue";

import bus from "@/utils/bus";
import GeoJsonLoader from "@/utils/geoJsonLoader";
import { initMap } from "@/utils/initMap";

import InfoPanel from "./InfoPanel.vue";
import { createImageryLayers } from "@/config/map-baselayer";
// @ts-ignore
import beijingDistricts from "@/data/beijingDistricts.json";

const { vecLayer } = createImageryLayers();

let viewer: Cesium.Viewer;
const regions = ref<RegionInfo[]>([]);

const rightInfo = reactive({
  name: "",
});
const loadJSONData = async (viewer: Cesium.Viewer) => {
  try {
    // 添加到Cesium查看器
    const dataSource = await GeoJsonLoader.addGeoJsonToViewer(
      viewer,
      beijingDistricts,
      {
        outlineColor: Cesium.Color.RED, // 使用红色边界
        outlineWidth: 3, // 边界宽度
        alpha: 0, // 不显示填充色
        name: "北京市区县",
      },
    );

    // 提取属性信息
    const properties =
      GeoJsonLoader.extractPropertiesFromGeoJson(beijingDistricts);

    // 添加到区域列表
    properties.forEach((prop) => {
      regions.value.push({
        ...prop,
        visible: true,
        dataSource,
      });
    });

    // 为区域添加悬浮和点击事件
    GeoJsonLoader.addRegionEvents(viewer, dataSource, {
      onClick: (entity) => {
        // 高亮显示点击的区域
        GeoJsonLoader.highlightRegion(entity);

        // 查找对应的区域信息
        const regionInfo = regions.value.find(
          (region) =>
            // region.dataSource === dataSource &&
            entity.name === region.name || entity.id === region.id,
        );

        if (regionInfo) {
          rightInfo.name = regionInfo.name;
          // 显示区域详情，并传递实体对象
          bus.emit("showRegionInfo", {
            region: regionInfo,
            dataSource,
            entity,
          });
        }
      },
    });

    // 飞行到区域
    GeoJsonLoader.flyToGeoJson(viewer, dataSource);
    // const entities = dataSource.entities.values;
    // viewer.zoomTo(dataSource).then(() => {
    //   ElMessage.success(`成功加载 ${properties.length} 个北京市区县数据`);
    // });
  } catch (error) {
    console.error("加载示例数据失败:", error);
    ElMessage.error("加载示例数据失败");
  }
};

const viewerDivRef = ref<HTMLDivElement>();

onMounted(async () => {
  if (viewer) return;
  viewer = await initMap(viewerDivRef);
  viewer.imageryLayers.add(vecLayer);
  loadJSONData(viewer);
});
</script>

<template>
  <div class="day-night-viewer mainContainer">
    <div id="cesium-viewer" ref="viewerDivRef"></div>
    <InfoPanel :name="rightInfo.name" />
  </div>
</template>
