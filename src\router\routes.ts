import { RouteRecordRaw } from "vue-router";

export const routes: Array<RouteRecordRaw> = [
  {
    path: "/map",
    name: "Map",
    component: () => import("@/views/map/map.vue"),
    meta: {
      title: "示例聚合v0",
      description: "主地图页面，展示3D地图视图",
    },
  },
  {
    path: "/",
    name: "Home",
    component: () => import("@/views/app/index.vue"),
    meta: {
      title: "应用",
      description: "应用主页面",
    },
  },
  {
    path: "/radian",
    name: "radian",
    component: () => import("@/widgets/Radian/index.vue"),
    meta: {
      title: "辐射点",
      description: "辐射点示例页面(WIP)",
    },
  },
  {
    path: "/fire",
    name: "fire",
    component: () => import("@/widgets/Fire/index.vue"),
    meta: {
      title: "爆炸点",
      description: "爆炸点示例页面(WIP)",
    },
  },
  {
    path: "/swipe",
    name: "swipe",
    component: () => import("@/widgets/Swipe/index.vue"),
    meta: {
      title: "卷帘对比",
      description: "图层卷帘对比功能",
    },
  },
  {
    path: "/primitives",
    name: "primitives",
    component: () => import("@/widgets/Primitives/index.vue"),
    meta: {
      title: "标点",
      description: "标点示例页面",
    },
  },
  {
    path: "/region",
    name: "region",
    component: () => import("@/widgets/Region/index.vue"),
    meta: {
      title: "区域",
      description: "区域选择与显示功能",
    },
  },
  {
    path: "/building",
    name: "building",
    component: () => import("@/widgets/Building/index.vue"),
    meta: {
      title: "建筑",
      description: "3D建筑展示与管理功能",
    },
  },
  {
    path: "/typhoon",
    name: "typhoon",
    component: () => import("@/widgets/TyphoonTrack/index.vue"),
    meta: {
      title: "台风轨迹(WIP)",
      description: "台风轨迹追踪与显示功能",
    },
  },
  {
    path: "/plan",
    name: "plan",
    component: () => import("@/widgets/AircraftViewer/AircraftViewer.vue"),
    meta: {
      title: "飞行计划",
      description: "飞行器查看与飞行路径规划功能",
    },
  },
  {
    path: "/daynight",
    name: "daynight",
    component: () => import("@/widgets/DayNightViewer/index.vue"),
    meta: {
      title: "昼夜切换",
      description: "地图昼夜效果切换功能",
    },
  },
];
