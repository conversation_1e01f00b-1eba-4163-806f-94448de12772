<!-- 卷帘工具 -->
<!-- https://sandcastle.cesium.com/?src=SplitDirection.html -->
<script setup lang="ts">
import * as Cesium from "cesium";

import { onMounted, onUnmounted, ref } from "vue";

import { ElButton } from "element-plus";

import { initMap } from "@/utils/initMap";

import { createImageryLayers } from "@/config/map-baselayer";

let viewer: Cesium.Viewer;
const viewerDivRef = ref<HTMLDivElement>();
const swipePosition = ref(0.5); // 0~1，表示卷帘线位置

const dragging = ref(false);

const onMouseMove = (e: MouseEvent) => {
  if (!dragging.value) return;
  const rect = (viewerDivRef.value as HTMLDivElement).getBoundingClientRect();
  swipePosition.value = Math.min(
    Math.max((e.clientX - rect.left) / rect.width, 0),
    1,
  );
  viewer.scene.splitPosition = swipePosition.value;
};

const onMouseUp = () => {
  dragging.value = false;
  window.removeEventListener("mousemove", onMouseMove);
  window.removeEventListener("mouseup", onMouseUp);
};

const startDrag = () => {
  dragging.value = true;
  window.addEventListener("mousemove", onMouseMove);
  window.addEventListener("mouseup", onMouseUp);
};

onMounted(async () => {
  // TODO
  if (viewer) return;
  viewer = await initMap(viewerDivRef);
  // viewer.imageryLayers.removeAll();

  const { imgLayer, ciaLayer, vecLayer } = createImageryLayers();
  imgLayer.splitDirection = Cesium.SplitDirection.LEFT;
  vecLayer.splitDirection = Cesium.SplitDirection.RIGHT;

  viewer.imageryLayers.add(imgLayer);
  viewer.imageryLayers.add(vecLayer);

  viewer.scene.splitPosition = swipePosition.value;
  viewer.camera.flyHome(0);
});

const resetSwipe = () => {
  swipePosition.value = 0.5;
  viewer.scene.splitPosition = 0.5;
};
onUnmounted(() => {
  if (viewer) {
    // viewer.imageryLayers.remove(imgLayer, false);
    // viewer.imageryLayers.remove(vecLayer, false);
  }
});
</script>

<template>
  <div class="relative w-full h-full">
    <div ref="viewerDivRef" class="w-full h-full"></div>
    <!-- 卷帘线 -->
    <div
      class="swipe-bar"
      :style="{ left: `${swipePosition * 100}%` }"
      @mousedown="startDrag"
    >
      <div class="swipe-line"></div>
      <div class="swipe-handle">
        <span class="swipe-arrow left"></span>
        <span class="swipe-arrow right"></span>
      </div>
    </div>
    <!-- 工具栏 -->
    <div class="absolute top-2 left-2 z-20">
      <ElButton @click="resetSwipe" type="primary" size="small"
        >重置卷帘</ElButton
      >
    </div>
  </div>
</template>

<style scoped>
.swipe-bar {
  @apply absolute top-0 bottom-0 z-20 flex flex-col items-center cursor-ew-resize select-none;
  width: 16px;
  transform: translateX(-50%);
}
.swipe-line {
  width: 4px;
  height: 100%;
  background: #222c38;
  opacity: 0.85;
  border-radius: 2px;
  box-shadow:
    0 0 8px #00fff7,
    0 0 2px #222c38;
}
.swipe-handle {
  @apply absolute left-1/2 top-1/2 flex items-center justify-center;
  width: 28px;
  height: 28px;
  background: linear-gradient(145deg, #232a3a 60%, #00fff7 100%);
  border: 2px solid #00fff7;
  border-radius: 50%;
  box-shadow:
    0 0 12px #00fff7,
    0 0 2px #222c38;
  transform: translate(-50%, -50%);
  position: absolute;
}
.swipe-arrow {
  display: inline-block;
  width: 0;
  height: 0;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
  margin: 0 2px;
}
.swipe-arrow.left {
  border-right: 10px solid #00fff7;
}
.swipe-arrow.right {
  border-left: 10px solid #00fff7;
}
</style>
