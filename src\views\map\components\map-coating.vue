<script setup lang="ts">
import { reactive, ref } from "vue";

import tool from "~/src/components/tool/tool";

import bus from "~/src/utils/bus";

const state = reactive({
  currentMousePosition: {
    lon: "138.245895",
    lat: "22.664534",
    height: "5000.000",
  },
});

const currentLevel = ref("4");

// 封装处理bus事件中鼠标位置更新的逻辑
const handleMouseMove = (res: any) => {
  state.currentMousePosition = res;
};
const handleMouseWheel = (res: any) => {
  currentLevel.value = res.level;
  state.currentMousePosition.height = res.height;
};

// TODO: remove bus
onMounted(async () => {
  bus.on("mouseMove", handleMouseMove);
  bus.on("mouseWheel", handleMouseWheel);
});
</script>
<template>
  <div class="map_coating">
    <div>版本号：V1.0</div>
    <div>经度：{{ state.currentMousePosition.lon }} °</div>
    <div>纬度：{{ state.currentMousePosition.lat }} °</div>
    <div>
      视角高：{{ tool.distanceUnitConvert(state.currentMousePosition.height) }}
    </div>
    <div>层级：{{ currentLevel }}</div>
  </div>
</template>
<style lang="scss" scoped>
.map_coating {
  position: fixed;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: aliceblue;
  div {
    padding: 0 10px;
  }
  div:last-child {
    flex: none;
    width: 80px !important;
  }
}
</style>
