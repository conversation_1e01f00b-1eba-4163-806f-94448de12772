<template>
  <!-- GIS功能模块弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    title="GIS功能模块"
    width="70%"
    top="10vh"
    class="gis-modules-dialog"
    :show-close="true"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
  >
    <div class="w-full h-full overflow-auto">
      <GIS />
    </div>
    <!-- <template #footer>
      <div class="flex justify-between items-center text-sm text-gray-500">
        <span>按 ESC 关闭</span>
        <span>按 Enter 进入选中模块</span>
      </div>
    </template> -->
  </el-dialog>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from "vue";

import GIS from "@/views/app/index.vue";

// 控制弹窗显示状态
const dialogVisible = ref(false);
const searchInput = ref<HTMLInputElement | null>(null);
const searchQuery = ref("");

// 监听弹窗状态，当弹窗打开时聚焦搜索框
watch(dialogVisible, (newVal) => {
  if (newVal) {
    // 等待DOM更新后聚焦搜索框
    setTimeout(() => {
      if (searchInput.value) {
        searchInput.value.focus();
      }
    }, 100);
  } else {
    // 关闭时清空搜索内容
    searchQuery.value = "";
  }
});

// 键盘快捷键处理
const handleKeyDown = (event: KeyboardEvent) => {
  // Ctrl+K 打开GIS功能模块弹窗
  if (event.ctrlKey && event.key === "k") {
    event.preventDefault(); // 阻止默认行为
    openDialog();
  }
};

// 打开弹窗
const openDialog = () => {
  dialogVisible.value = true;
};

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false;
};

// 对外暴露方法
defineExpose({
  openDialog,
  closeDialog,
});

onMounted(() => {
  // 注册全局键盘事件监听
  window.addEventListener("keydown", handleKeyDown);
});

onBeforeUnmount(() => {
  // 移除键盘事件监听
  window.removeEventListener("keydown", handleKeyDown);
});
</script>
