import { createApp } from "vue";

import "@/assets/styles/global.css";

import App from "./App.vue";
import "./style.css";
import router from "@/router";
import "@/scss/app.scss";
// import "@/scss/theme01.scss";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import zhCn from "element-plus/es/locale/lang/zh-cn";
// import "element-plus/theme-chalk/dark/css-vars.css";
import { createPinia } from "pinia";

const pinia = createPinia();

const app = createApp(App);
app.use(pinia).use(router);
app.use(ElementPlus, {
  locale: zhCn,
  size: "small",
});

app.mount("#app");
