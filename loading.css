       html,
       body,
       #app {
       	height: 100%;
       	margin: 0px;
       	padding: 0px;
       }

       #loader-wrapper {
       	position: fixed;
       	top: 0;
       	left: 0;
       	width: 100%;
       	height: 100%;
       	z-index: 999999;
       }

       #loader {
       	display: block;
       	position: relative;
       	left: 50%;
       	top: 50%;
       	width: 150px;
       	height: 150px;
       	margin: -75px 0 0 -75px;
       	border-radius: 50%;
       	border: 3px solid transparent;
       	border-top-color: #FFF;
       	-webkit-animation: spin 2s linear infinite;
       	-ms-animation: spin 2s linear infinite;
       	-moz-animation: spin 2s linear infinite;
       	-o-animation: spin 2s linear infinite;
       	animation: spin 2s linear infinite;
       	z-index: 1001;
       }

       #loader:before {
       	content: "";
       	position: absolute;
       	top: 5px;
       	left: 5px;
       	right: 5px;
       	bottom: 5px;
       	border-radius: 50%;
       	border: 3px solid transparent;
       	border-top-color: #FFF;
       	-webkit-animation: spin 3s linear infinite;
       	-moz-animation: spin 3s linear infinite;
       	-o-animation: spin 3s linear infinite;
       	-ms-animation: spin 3s linear infinite;
       	animation: spin 3s linear infinite;
       }

       #loader:after {
       	content: "";
       	position: absolute;
       	top: 15px;
       	left: 15px;
       	right: 15px;
       	bottom: 15px;
       	border-radius: 50%;
       	border: 3px solid transparent;
       	border-top-color: #FFF;
       	-moz-animation: spin 1.5s linear infinite;
       	-o-animation: spin 1.5s linear infinite;
       	-ms-animation: spin 1.5s linear infinite;
       	-webkit-animation: spin 1.5s linear infinite;
       	animation: spin 1.5s linear infinite;
       }


       @-webkit-keyframes spin2 {
       	0% {
       		-webkit-transform: rotate(0deg);
       		-ms-transform: rotate(0deg);
       		transform: rotate(0deg);
       	}

       	100% {
       		-webkit-transform: rotate(360deg);
       		-ms-transform: rotate(360deg);
       		transform: rotate(360deg);
       	}
       }

       @keyframes spin2 {
       	0% {
       		-webkit-transform: rotate(0deg);
       		-ms-transform: rotate(0deg);
       		transform: rotate(0deg);
       	}

       	100% {
       		-webkit-transform: rotate(360deg);
       		-ms-transform: rotate(360deg);
       		transform: rotate(360deg);
       	}
       }


       #loader-wrapper .loader-section {
       	position: fixed;
       	top: 0;
       	width: 51%;
       	height: 100%;
       	background: #7171C6;
       	z-index: 1000;
       	-webkit-transform: translateX(0);
       	-ms-transform: translateX(0);
       	transform: translateX(0);
       }

       #loader-wrapper .loader-section.section-left {
       	left: 0;
       }

       #loader-wrapper .loader-section.section-right {
       	right: 0;
       }


       .loaded #loader-wrapper .loader-section.section-left {
       	-webkit-transform: translateX(-100%);
       	-ms-transform: translateX(-100%);
       	transform: translateX(-100%);
       	-webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
       	transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
       }

       .loaded #loader-wrapper .loader-section.section-right {
       	-webkit-transform: translateX(100%);
       	-ms-transform: translateX(100%);
       	transform: translateX(100%);
       	-webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
       	transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
       }

       .loaded #loader {
       	opacity: 0;
       	-webkit-transition: all 0.3s ease-out;
       	transition: all 0.3s ease-out;
       }

       .loaded #loader-wrapper {
       	visibility: hidden;
       	-webkit-transform: translateY(-100%);
       	-ms-transform: translateY(-100%);
       	transform: translateY(-100%);
       	-webkit-transition: all 0.3s 1s ease-out;
       	transition: all 0.3s 1s ease-out;
       }

       .no-js #loader-wrapper {
       	display: none;
       }

       .no-js h1 {
       	color: #222222;
       }

       #loader-wrapper .load_title {
       	font-family: 'Open Sans';
       	color: #FFF;
       	font-size: 19px;
       	width: 100%;
       	text-align: center;
       	z-index: 9999999999999;
       	position: absolute;
       	top: 60%;
       	opacity: 1;
       	line-height: 30px;
       }

       #loader-wrapper .load_title span {
       	font-weight: normal;
       	font-style: italic;
       	font-size: 13px;
       	color: #FFF;
       	opacity: 0.5;
       }

       .Loading_title_cn {
       	position: absolute;
       	left: 50%;
       	font-size: 3vh;
       	font-family: 'YouSheBiaoTiHei';
       	/* // font-family: 'Microsoft YaHei', sans-serif; */
       	top: 56%;
       	transform: translate(-50%, -50%);
       	color: #ffffff9a;
       }

       .Loading_title_en {
       	position: absolute;
       	left: 50%;
       	font-size: 2.0vh;
       	font-family: 'YouSheBiaoTiHei';
       	top: 60%;
       	transform: translate(-50%, -50%);
       	color: #ffffff9a;
       }

       .loaderbox {
       	width: 80vw;
       	height: 90vh;
       	display: flex;
       	align-items: center;
       	justify-content: center;
       	position: absolute;
       	top: 50%;
       	left: 50%;
       	transform: translate(-50%, -55%);

       }

       .Loading_box {
       	width: 100%;
       	height: 100%;
       	position: relative;
       	background-color: #212121;
       	z-index: 9999;

       	&--model {
       		width: 100vw;
       		height: 100vh;
       		position: relative;
       	}

       	&--bar {
       		position: absolute;
       		/* // background-color: red; */
       		width: 50%;
       		height: 60px;
       		bottom: 25vh;
       		left: 50%;
       		transform: translateX(-50%);
       	}
       }

       .Loading-bar {
       	position: absolute;
       	bottom: 0;
       	/* left: 5vw; */
       	width: 100%;
       	height: 12px;
       	background: #000;
       	border-radius: 50px;
       	overflow: hidden;
       }

       .Loading-bar_inner {
       	background: #e7f4ff;
       	box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.3);
       	border-radius: 50px;
       	transition: width 0.3s ease;
       }

       .Loading-progress {
       	color: #ffffff63;
       	font-size: 11px;
       	position: absolute;
       	bottom: 22px;
       	right: 0;
       	--w: 10ch;
       	font-weight: bold;
       }

       .Loading-text {
       	/* // position: absolute;
// margin-left: 2%; */
       	letter-spacing: 2px;
       	color: #ffffff63;
       }

       /* From Uiverse.io by alexmaracinaru */
       .pyramid-loader {
       	position: relative;
       	width: 300px;
       	height: 150px;
       	display: block;
       	transform-style: preserve-3d;
       	transform: rotateX(-20deg);
       }

       .wrapper {
       	position: relative;
       	width: 100%;
       	height: 100%;
       	transform-style: preserve-3d;
       	animation: spin 4s linear infinite;
       }

       @keyframes spin2 {
       	100% {
       		transform: rotateY(360deg);
       	}
       }

       .pyramid-loader .wrapper .side {
       	width: 70px;
       	height: 70px;
       	background: linear-gradient(to bottom right, #FFA500, #FF4500);
       	position: absolute;
       	top: 0;
       	left: 0;
       	right: 0;
       	bottom: 0;
       	margin: auto;
       	transform-origin: center top;
       	clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
       }

       .pyramid-loader .wrapper .side1 {
       	transform: rotateZ(-30deg) rotateY(90deg);
       	background: linear-gradient(to bottom right, #FF4500, #FFA500);
       }

       .pyramid-loader .wrapper .side2 {
       	transform: rotateZ(30deg) rotateY(90deg);
       	background: linear-gradient(to bottom right, #FFA500, #FF4500);
       }

       .pyramid-loader .wrapper .side3 {
       	transform: rotateX(30deg);
       	background: linear-gradient(to bottom right, #FFA500, #FF4500);
       }

       .pyramid-loader .wrapper .side4 {
       	transform: rotateX(-30deg);
       	background: linear-gradient(to bottom right, #FF4500, #FFA500);
       }

       .pyramid-loader .wrapper .shadow {
       	width: 60px;
       	height: 60px;
       	background: #FF8C00;
       	position: absolute;
       	top: 0;
       	left: 0;
       	right: 0;
       	bottom: 0;
       	margin: auto;
       	transform: rotateX(90deg) translateZ(-40px);
       	filter: blur(12px);
       }