<!-- 标点-高性能(primitives) -->
<script setup lang="ts">
import * as Cesium from "cesium";

import { computed, onMounted, onUnmounted, reactive, ref } from "vue";

import pointSvgImage from "@/assets/svg/point-cyan.svg";

import { addConvertPointEvent } from "@/utils/convertPoint";
import { initMap } from "@/utils/initMap";

import { getGeoData } from "@/api/geo";
// 导入Element Plus图标
import { Close, House, InfoFilled, Location } from "@element-plus/icons-vue";

let viewer: Cesium.Viewer;
const viewerDivRef = ref<HTMLDivElement>();
// let pointFeatures = [];

// 存储点位数据，用于点击时获取信息
const pointsData = reactive<Record<string, any>>({});

// 弹窗相关
const showPopup = ref(false);
const popupPosition = reactive({ left: "0px", top: "0px" });
const currentPointInfo = ref<any>(null);
// 当前选中的点位的3D坐标
let selectedPointPosition: Cesium.Cartesian3 | null = null;
// 添加位置动画控制
const isPositionChanging = ref(false);
// 当前点位序号
const currentPointIndex = ref(0);
// 控制弹窗关闭动画
const isClosing = ref(false);

// 提取当前点位的属性，过滤掉不需要显示的属性
const pointProperties = computed(() => {
  if (!currentPointInfo.value) return [];

  const properties: { label: string; value: any }[] = [];

  // 遍历所有属性
  Object.entries(currentPointInfo.value).forEach(([key, value]) => {
    // 排除坐标和名称，因为它们会单独显示
    if (key !== "coordinates" && key !== "name") {
      properties.push({
        label: key,
        value,
      });
    }
  });

  return properties;
});

// 控制动画的状态
const animationComplete = ref(false);

// 获取格式化后的坐标
const formattedCoordinates = computed(() => {
  if (!currentPointInfo.value?.coordinates) return "";

  const [lon, lat] = currentPointInfo.value.coordinates;
  return `${Number(lon).toFixed(6)}, ${Number(lat).toFixed(6)}`;
});

// 打点
const getJson = () => {
  getGeoData().then(({ data }) => {
    const { features } = data;
    // pointFeatures = features;
    formatData(features);

    viewer.camera.setView({
      // 从以度为单位的经度和纬度值返回笛卡尔3位置。
      destination: Cesium.Cartesian3.fromDegrees(120.36, 36.09, 40000),
    });
  });
};

let billboardsCollectionCombine = new Cesium.BillboardCollection();
let primitivesCollection: Cesium.PrimitiveCollection | null = null;
let primitives: Cesium.PrimitiveCollection | null = null;
let billboardsCollection: Cesium.BillboardCollection | null = null;

const formatData = (features: any) => {
  for (let i = 0; i < features.length; i++) {
    const feature = features[i];
    // 每个点位的坐标
    const coordinates = feature.geometry.coordinates;
    // 将坐标处理成3D笛卡尔点
    const position = Cesium.Cartesian3.fromDegrees(
      coordinates[0],
      coordinates[1],
      0, // 高度会影响点位偏移
    );
    const name = feature.properties.name;

    // 存储点位信息，使用坐标作为key
    const pointKey = `${coordinates[0]}_${coordinates[1]}`;
    pointsData[pointKey] = {
      name,
      ...feature.properties,
      coordinates,
      index: i + 1, // 添加序号，从1开始
    };

    // 画普通的点
    // pointCollection.add({
    //   position,
    //   color: Cesium.Color.CYAN,
    //   pixelSize: 36,
    // })
    // 带图片的点
    if (billboardsCollection) {
      // 添加Billboard到集合中
      billboardsCollection.add({
        image: pointSvgImage,
        width: 32,
        height: 32,
        position,
        id: pointKey, // 使用唯一标识便于后续查找
        // disableDepthTestDistance: 50000000,
        // heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
        // sizeInMeters: true
      });
    }
    // TODO:如果text是动态的，有性能问题；
    // labelCollection.add({
    //   position,
    //   blendOption: Cesium.BlendOption.TRANSLUCENT, // 半透明，提高性能2倍
    //   text: name,
    //   font: "bold 15px Microsoft YaHei",
    //   // 竖直对齐方式
    //   verticalOrigin: Cesium.VerticalOrigin.CENTER,
    //   // 水平对齐方式
    //   horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
    //   // 偏移量
    //   pixelOffset: new Cesium.Cartesian2(15, 0),
    // })
  }
};

// 更新弹窗位置
const updatePopupPosition = () => {
  if (!selectedPointPosition || !showPopup.value) return;

  const windowCoordinates = Cesium.SceneTransforms.wgs84ToWindowCoordinates(
    viewer.scene,
    selectedPointPosition,
  );

  if (windowCoordinates) {
    // 如果是位置变化，添加动画标记
    if (
      showPopup.value &&
      (popupPosition.left !== `${windowCoordinates.x}px` ||
        popupPosition.top !== `${windowCoordinates.y - 10}px`)
    ) {
      isPositionChanging.value = true;

      // 设置新位置
      popupPosition.left = `${windowCoordinates.x}px`;
      popupPosition.top = `${windowCoordinates.y - 10}px`;

      // 动画结束后重置标记
      setTimeout(() => {
        isPositionChanging.value = false;
      }, 300); // 与CSS过渡时间匹配
    } else {
      // 初始显示时直接设置位置，无需动画
      popupPosition.left = `${windowCoordinates.x}px`;
      popupPosition.top = `${windowCoordinates.y - 10}px`;
    }
  } else {
    // 如果转换失败（点不在可视区域内），隐藏弹窗
    showPopup.value = false;
  }
};

// 点击事件处理
const handleMapClick = (event: any) => {
  // 如果弹窗正在关闭动画中，不处理新的点击
  if (isClosing.value) return;

  // 隐藏之前的弹窗
  showPopup.value = false;
  selectedPointPosition = null;

  // 获取点击位置
  const pickedObject = viewer.scene.pick(event.position);

  if (
    Cesium.defined(pickedObject) &&
    pickedObject.primitive instanceof Cesium.Billboard &&
    pickedObject.primitive.id
  ) {
    // 获取点击的billboard的ID
    const pointKey = pickedObject.primitive.id as string;

    // 获取点位信息
    const pointInfo = pointsData[pointKey];
    if (pointInfo) {
      currentPointInfo.value = pointInfo;
      currentPointIndex.value = pointInfo.index || 0; // 设置当前点位序号

      // 保存当前选中点的3D坐标，用于后续更新弹窗位置
      selectedPointPosition = pickedObject.primitive.position;

      // 将点位移动到屏幕中央
      centerPointOnScreen(pointInfo.coordinates);

      // 如果已经有弹窗显示，先重置动画状态
      if (showPopup.value) {
        animationComplete.value = false; // 重置内容动画状态
        // 更新弹窗位置 - 会触发位置动画
        updatePopupPosition();

        // 启动内容动画
        setTimeout(() => {
          animationComplete.value = true;
        }, 100);
      } else {
        // 首次显示弹窗
        selectedPointPosition = null;
        animationComplete.value = false;
        currentPointInfo.value = pointInfo;

        // 保存当前选中点的3D坐标，用于后续更新弹窗位置
        selectedPointPosition = pickedObject.primitive.position;

        // 先确保动画状态重置，然后在下一个渲染周期显示弹窗
        setTimeout(() => {
          // 更新弹窗位置并显示
          updatePopupPosition();
          showPopup.value = true;

          // 启动动画
          setTimeout(() => {
            animationComplete.value = true;
          }, 100);
        }, 0);
      }
    }
  } else {
    // 点击空白处，隐藏弹窗
    closePopup();
  }
};

// 将点位移动到屏幕中央
const centerPointOnScreen = (coordinates: number[]) => {
  const [lon, lat] = coordinates;

  // 获取当前相机高度
  const currentHeight = viewer.camera.positionCartographic.height;

  // 使用flyTo而不是setView，提供平滑的过渡动画
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(lon, lat, currentHeight),
    orientation: {
      heading: viewer.camera.heading,
      pitch: viewer.camera.pitch,
      roll: viewer.camera.roll,
    },
    duration: 1.0, // 动画持续时间，单位秒
    easingFunction: Cesium.EasingFunction.CUBIC_IN_OUT, // 平滑的加减速
    complete: () => {
      console.log("相机移动到点位完成");
    },
  });
};

// 关闭弹窗
const closePopup = () => {
  // 先设置关闭动画标记
  isClosing.value = true;

  // 等待动画完成后再隐藏弹窗
  setTimeout(() => {
    showPopup.value = false;
    selectedPointPosition = null;
    animationComplete.value = false; // 重置动画状态
    isClosing.value = false; // 重置关闭动画标记
  }, 300); // 与CSS过渡时间匹配
};

let cameraChangeListener: any = null;
let postRenderListener: any = null;

onMounted(async () => {
  if (viewer) return;
  viewer = await initMap(viewerDivRef);
  viewer.camera.setView({
    // 从以度为单位的经度和纬度值返回笛卡尔3位置。
    destination: Cesium.Cartesian3.fromDegrees(120.36, 36.09, 40000),
  });

  billboardsCollection = viewer.scene.primitives.add(
    new Cesium.BillboardCollection(),
  );

  // 添加点击事件监听
  viewer.screenSpaceEventHandler.setInputAction(
    handleMapClick,
    Cesium.ScreenSpaceEventType.LEFT_CLICK,
  );

  // 添加相机变化监听，当地图移动、缩放时更新弹窗位置
  cameraChangeListener =
    viewer.camera.changed.addEventListener(updatePopupPosition);

  // 添加场景渲染后的监听，用于在每帧渲染后更新弹窗位置，确保平滑跟随
  postRenderListener =
    viewer.scene.postRender.addEventListener(updatePopupPosition);
  nextTick(() => {
    getJson(); // 加载自动打点
    addConvertPointEvent(viewer);
  });
  // viewer.camera.changed.addEventListener(function () {
  //   console.log("地图视角正在变化中");
  //   updatePopupPosition();
  // });
  // const handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
  // handler.setInputAction(function (movement) {
  //   // console.log("开始拖动地图");
  //   updatePopupPosition();
  // }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

  // handler.setInputAction(function (movement) {
  //   // console.log("结束拖动地图");
  //   updatePopupPosition();
  // }, Cesium.ScreenSpaceEventType.LEFT_UP);
});

onUnmounted(() => {
  // 清除事件监听，防止内存泄漏
  if (cameraChangeListener) {
    viewer.camera.changed.removeEventListener(cameraChangeListener);
  }

  if (postRenderListener) {
    viewer.scene.postRender.removeEventListener(postRenderListener);
  }

  // 清除鼠标事件监听
  if (viewer && viewer.screenSpaceEventHandler) {
    viewer.screenSpaceEventHandler.removeInputAction(
      Cesium.ScreenSpaceEventType.MOUSE_MOVE,
    );
  }
});

const onClear = () => {
  //   handleClose();
  if (billboardsCollection) {
    billboardsCollection.removeAll(); // 打点清除
  }
  // 清空点位数据
  Object.keys(pointsData).forEach((key) => {
    delete pointsData[key];
  });
  // 隐藏弹窗
  showPopup.value = false;
  selectedPointPosition = null;
  //   billboardsCollection = null;
  //   primitives!.removeAll(); // 打点聚合
  //   primitivesCollection = null;
  //   billboardsCollectionCombine = null;
};
</script>

<template>
  <div class="mainContainer">
    <div id="cesium-viewer" ref="viewerDivRef"></div>
    <!-- <div class="fixed bottom-0 left-0">
      <button @click="getJson">打点</button>
      <button @click="onClear">清除打点</button>
    </div> -->

    <!-- 点位信息弹窗容器 - 使用fixed定位避免影响页面布局 -->
    <div class="popup-container">
      <div
        v-if="showPopup || isClosing"
        class="point-popup"
        :class="{
          'position-changing': isPositionChanging,
          'popup-closing': isClosing,
        }"
        :style="{ left: popupPosition.left, top: popupPosition.top }"
      >
        <div class="popup-content">
          <div class="popup-header">
            <div class="flex items-center">
              <div class="point-index">{{ currentPointIndex }}</div>
              <h3>{{ currentPointInfo?.name }}</h3>
            </div>
            <el-icon class="close-btn" @click="closePopup"><Close /></el-icon>
          </div>
          <div class="popup-body">
            <!-- 地址信息 -->
            <div
              class="info-item address-item"
              :class="{ 'animate-in': animationComplete }"
              style="--animation-delay: 0.1s"
            >
              <span class="info-label">
                <el-icon><House /></el-icon>地址:
              </span>
              <span class="info-value">
                {{ currentPointInfo?.address || "山东省青岛市北区杭州路80号" }}
              </span>
            </div>

            <!-- 其他属性信息 -->
            <div
              v-for="(prop, index) in pointProperties"
              :key="index"
              class="info-item"
              :class="{ 'animate-in': animationComplete }"
              :style="{
                '--animation-delay': `${0.2 + index * 0.1}s`,
              }"
            >
              <span class="info-label">
                <el-icon><InfoFilled /></el-icon>{{ prop.label }}:
              </span>
              <span class="info-value">{{ prop.value }}</span>
            </div>

            <!-- 坐标信息 -->
            <div
              v-if="formattedCoordinates"
              class="info-item"
              :class="{ 'animate-in': animationComplete }"
              :style="{
                '--animation-delay': `${0.2 + pointProperties.length * 0.1}s`,
              }"
            >
              <span class="info-label">
                <el-icon><Location /></el-icon>坐标:
              </span>
              <span class="info-value">{{ formattedCoordinates }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.popup-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
  overflow: hidden;
}

.point-popup {
  position: absolute;
  transform: translate(-50%, -100%);
  z-index: 1000;
  pointer-events: auto;
  transition:
    left 0.05s,
    top 0.05s,
    opacity 0.3s ease,
    transform 0.3s ease;
  margin-top: -15px;
  opacity: 1;
  /* max-height: 80vh;
  overflow: auto; */
}

.point-popup.position-changing {
  transition:
    left 0.3s cubic-bezier(0.34, 1.56, 0.64, 1),
    top 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.point-popup.popup-closing {
  opacity: 0;
  transform: translate(-50%, -90%);
  pointer-events: none;
}

.popup-content {
  @apply bg-slate-800/65 text-white rounded-lg shadow-lg;
  border: 2px solid #0fc6c2;
  min-width: 272px;
  max-width: 500px;
  /* position: relative; */
  backdrop-filter: blur(5px);
}

.popup-content::before {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #0fc6c2;
  z-index: 1;
}

.popup-header {
  @apply flex justify-between items-center p-2 bg-slate-900/80 border-b-2 border-cyan-400 rounded-t-lg;
  position: relative;
}

.point-index {
  @apply flex items-center justify-center text-sm bg-cyan-500 text-slate-900 rounded-full mr-2 size-4;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 8px rgba(15, 198, 194, 0.6);
  font-size: 12px;
}

.popup-header::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(90deg, rgba(15, 198, 194, 0.1), transparent);
  pointer-events: none;
}

.popup-header h3 {
  @apply text-lg font-bold m-0 text-cyan-300;
}

.close-btn {
  @apply text-white text-xl font-bold bg-transparent border-none cursor-pointer text-cyan-300;
  z-index: 1;
}

.popup-body {
  @apply p-4;
}

.info-item {
  @apply mb-2 text-sm flex;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.info-item.animate-in {
  opacity: 1;
  transform: translateY(0);
  transition-delay: var(--animation-delay, 0s);
}

.popup-closing .info-item {
  opacity: 0;
  transform: translateY(10px);
  transition-delay: 0s !important;
  transition-duration: 0.2s;
}

.address-item {
  @apply mb-3 pb-2 border-b border-cyan-800/50;
}

.info-label {
  @apply font-bold mr-2 text-cyan-300 w-20 flex-shrink-0 flex items-center;
}

.info-label .el-icon {
  @apply mr-1 text-cyan-400;
  font-size: 16px;
}

.info-value {
  @apply text-gray-100 flex-1;
}
</style>
