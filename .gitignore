components.d.ts
auto-imports.d.ts
# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

dist
dist-ssr
*.local


# 忽略node_modules目录
node_modules/

# 忽略Logs
*.log

# 忽略/dist目录和开发资源有减少占用，相对.gitignore文件所在目录
/dist

# 忽略IDE的配置文件
.idea/
*.sw*

# Editor directories and files
.vscode/*
.history/*
!.vscode/extensions.json
# .idea
# .DS_Store
# *.suo
# *.ntvs*
# *.njsproj
# *.sln
# *.sw?
# # Copy all files in edits
# *copy*.ts*
# *copy*.js*
# *-d3*.js*
# *copy*.vue*
# *copy*.css*
# *copy*.scss*
# *copy*.glb*
# *copy*.gltf*
# *copy*.obj*
# *.glb*
# *.gltf*
# *.obj*

./components.d.ts
vite.config.ts.timestamp*.mjs
