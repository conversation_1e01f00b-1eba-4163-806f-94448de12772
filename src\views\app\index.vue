<template>
  <div class="bg-gray-900 text-white p-0 m-0 h-screen overflow-auto">
    <!-- Header with top-right background image -->
    <div class="relative w-full">
      <!-- Background image positioned in top-right -->
      <div
        class="fixed top-4 left-2/3 translate-x-[-50%] w-48 h-48 overflow-hidden z-0 rounded-full"
      >
        <img
          src="@/assets/images/widgets/earth.avif"
          alt="Earth background"
          class="size-48 object-cover earth-rotation rounded-full"
          style="scale: 1.25"
        />
        <!-- 处理图片和背景融为一体 -->
        <div
          class="absolute inset-0 bg-gradient-to-l from-transparent to-gray-900 rounded-full"
        ></div>
        <div
          class="absolute inset-0 bg-gradient-to-b from-transparent to-gray-900 rounded-full"
        ></div>
      </div>

      <!-- Header content -->
      <div class="relative z-10 max-w-6xl mx-auto px-4 pt-8">
        <h1
          class="text-4xl font-bold text-center text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-300 drop-shadow-lg"
        >
          GIS 功能模块
        </h1>
        <div
          class="w-64 h-1 mx-auto mt-3 bg-gradient-to-r from-blue-500 to-cyan-400 rounded-full"
        ></div>
      </div>

      <!-- Decorative tech elements -->
      <!-- <div class="absolute top-0 right-0 w-1/3 h-72 pointer-events-none z-10">
        <div
          class="absolute top-5 right-12 w-20 h-20 border border-blue-400/30 rounded-full animate-pulse"
        ></div>
        <div
          class="absolute top-24 right-24 w-12 h-12 border border-cyan-400/20 rounded-full"
        ></div>
      </div> -->
    </div>

    <div class="max-w-6xl mx-auto px-4 pt-6 relative z-10">
      <!-- 搜索框 -->
      <div class="mb-8 max-w-md mx-auto">
        <div class="relative">
          <input
            v-model="searchQuery"
            type="text"
            autofocus
            placeholder="搜索模块..."
            class="w-full pl-4 py-3 focus:rounded-sm bg-gray-800 border border-gray-700 text-white focus:outline-none focus:ring-2 border-none rounded-md focus:ring-blue-500 focus:border-transparent placeholder-gray-500"
          />
          <div
            class="absolute inset-y-0 right-0 flex items-center pointer-events-none"
          >
            <Search class="size-4 text-gray-400" />
          </div>
        </div>
      </div>

      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10"
        v-if="filteredList.length > 0"
      >
        <div
          v-for="(item, index) in filteredList"
          :key="item.path"
          class="bg-gray-800 rounded-lg overflow-hidden border border-gray-700 group hover:border-blue-500 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105"
        >
          <router-link :to="item.path" class="block h-full no-underline">
            <div class="p-6">
              <div class="flex items-center mb-2">
                <div class="card-index">{{ index + 1 }}</div>
                <h2
                  class="text-xl font-semibold text-blue-400 group-hover:text-blue-300"
                >
                  {{ item.title || item.name }}
                </h2>
              </div>
              <p class="text-gray-400 text-sm">
                {{ item.description || "暂无描述" }}
              </p>
            </div>
            <div
              class="px-6 py-3 bg-gray-700 flex justify-between items-center"
            >
              <div class="text-xs font-medium text-gray-400 flex items-center">
                <Folder class="mr-2 size-4" />
                {{ item.path }}
              </div>
              <span
                class="text-blue-400 group-hover:translate-x-1 transition-transform duration-300"
              >
                <i class="el-icon-right"></i>
              </span>
            </div>
          </router-link>
        </div>
      </div>

      <el-empty v-else description="未找到匹配的模块" class="mt-10" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";

import { routes } from "@/router/routes";
import { Folder, Search } from "@element-plus/icons-vue";

const searchQuery = ref("");

const list = routes
  .filter((route) => !["/", "/"].includes(route.path))
  .map((route) => {
    return {
      path: route.path,
      name: route.name,
      title: route.meta?.title,
      description: route.meta?.description,
    };
  });

const filteredList = computed(() => {
  if (!searchQuery.value) return list;

  const query = searchQuery.value.toLowerCase();
  return list.filter(
    (item) =>
      ((item.title || item.name || "") as string)
        .toLowerCase()
        .includes(query) ||
      ((item.description || "") as string).toLowerCase().includes(query) ||
      item.path.toLowerCase().includes(query),
  );
});
</script>

<style scoped>
.card-index {
  @apply flex items-center justify-center text-sm bg-blue-400 text-slate-900 rounded-full mr-3 size-4;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 8px rgba(15, 198, 194, 0.6);
  font-size: 12px;
}

.el-empty {
  --el-empty-fill-color-0: rgba(20, 27, 45, 0.7);
  --el-empty-fill-color-1: rgba(20, 27, 45, 0.5);
  --el-empty-fill-color-2: rgba(20, 27, 45, 0.3);
  --el-empty-fill-color-3: rgba(20, 27, 45, 0.1);
  --el-empty-fill-color-4: rgba(20, 27, 45, 0.05);
  --el-empty-fill-color-5: rgba(20, 27, 45, 0.02);
}

/* Add animation for tech style */
@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}
.animate-pulse {
  animation: pulse 4s ease-in-out infinite;
}

/* Earth rotation animation */
@keyframes earthRotate {
  0% {
    transform: rotate(0deg) scale(1.05);
  }
  100% {
    transform: rotate(360deg) scale(1.05);
  }
}

.earth-rotation {
  animation: earthRotate 60s linear infinite;
  /* transform-origin: center; */
  filter: brightness(1.1) contrast(1.1);
  transition: filter 0.5s ease;
}

/* .earth-rotation:hover {
  filter: brightness(1.2) contrast(1.2);
  animation-duration: 45s;
} */
</style>
