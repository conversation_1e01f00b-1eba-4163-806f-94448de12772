<template>
  <div id="customContainer" v-if="main">
    <div class="player">
      <el-tooltip content="快退" placement="bottom">
        <i
          class="iconfont font32 color999 gis-anniu_jiantouxiangzuo ml15"
          @click="clockControls('retreat')"
        ></i>
      </el-tooltip>
      <el-tooltip v-if="flag == 'start'" content="开始" placement="bottom">
        <i
          class="iconfont font32 color999 gis-bofang"
          @click="clockControls('start')"
        ></i>
      </el-tooltip>
      <el-tooltip v-if="flag == 'continue'" content="继续" placement="bottom">
        <i
          class="iconfont font32 color999 gis-bofang"
          @click="clockControls('continue')"
        ></i>
      </el-tooltip>
      <el-tooltip v-if="flag == 'pause'" content="暂停" placement="bottom">
        <i
          class="iconfont font32 color999 gis-zanting"
          @click="clockControls('pause')"
        ></i>
      </el-tooltip>

      <el-tooltip content="快进" placement="bottom">
        <i
          class="iconfont font32 color999 gis-anniu-jiantouxiangyou"
          @click="clockControls('forward')"
        ></i>
      </el-tooltip>
      <el-tooltip content="重置" placement="bottom">
        <i
          class="iconfont font32 color-warn gis-tingzhi"
          @click="clockControls('reset')"
        ></i>
      </el-tooltip>
      <el-tooltip content="关闭演示" placement="bottom">
        <i
          class="iconfont font32 color-danger gis-anniu_guanbi"
          @click="clockControls('end')"
        ></i>
      </el-tooltip>
      <span style="font-size: 13px; margin-left: 10px">倍速：</span>
      <el-select
        v-model="speed"
        size="small"
        class="speed"
        @change="changeSpeed"
        popper-class="speedSelect"
      >
        <el-option key="1" label="1X" value="1" />
        <el-option key="2" label="2X" value="2" />
        <el-option key="4" label="4X" value="4" />
        <el-option key="8" label="8X" value="8" />
        <el-option key="16" label="16X" value="16" />
        <el-option key="32" label="32X" value="32" />
        <el-option key="64" label="64X" value="64" />
        <el-option key="128" label="128X" value="128" />
      </el-select>
    </div>
    <div class="info pb15">
      <div class="weather pl10 mb10">
        <div class="title">天气信息</div>
        <div class="con">天气：🌤 多云转晴</div>
        <div class="con">温度：9 ~ 18 ℃</div>
        <div class="con">湿度：23%</div>
        <div class="con">风速：5m/s</div>
        <div class="con">气压：1023hPa</div>
      </div>
      <!-- <div class="units pl10">
                <div class="title">单位信息</div>
                <div class="con">红军：指挥部、坦克营、空军指挥部、干扰雷达</div>
                <div class="con">蓝军：战斗机</div>
            </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";

import { v4 as uuid4 } from "uuid";
import {
  BaseCoordinates,
  BlueForceAircrafts,
  RedForceUnits,
} from "~/src/data/landAirCombatData";
import bus from "~/src/utils/bus";

const main = ref(false);
const flag = ref("start");
const speed = ref("1");
const interval = ref();
const redEntityId = ref<string[]>([]);
const blueEntityId = ref<string[]>([]);
const radarEntityId = ref<string[]>([]);
const radarRotation = ref(0); // 雷达旋转角度
const radarAnimationId = ref<number | null>(null); // 雷达动画ID

// 一、初始化地图与viewer.clock时间配置
const init = async () => {
  main.value = true;
  const viewer = window.viewer;
  viewer.entities.removeAll();

  // 设置时间
  const startTime = Cesium.JulianDate.fromDate(new Date());
  const stopTime = Cesium.JulianDate.addSeconds(
    startTime,
    3600,
    new Cesium.JulianDate(),
  );
  const viewCenter = BaseCoordinates;
  viewer.clock.startTime = startTime.clone();
  viewer.clock.stopTime = stopTime.clone();
  viewer.clock.currentTime = startTime.clone(); // 确保当前时间设置为开始时间
  viewer.clock.multiplier = 20;
  viewer.clock.clockRange = Cesium.ClockRange.CLAMPED;
  viewer.timeline.zoomTo(startTime, stopTime);

  // 初始化时不自动播放，但确保时钟状态正确
  if (flag.value === "start") {
    viewer.clock.shouldAnimate = false;
  } else {
    viewer.clock.shouldAnimate = true;
  }

  // 设置相机视角
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(
      viewCenter.lon,
      viewCenter.lat,
      800000,
    ),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-90),
      roll: 0,
    },
  });

  // 添加红军单位
  addRedForceUnits(viewer, startTime);

  // 添加蓝军飞机
  addBlueForceAircrafts(viewer, startTime, stopTime);
};

// 启动雷达旋转动画
const startRadarAnimation = () => {
  // 如果已经有动画在运行，先停止它
  stopRadarAnimation();

  // 设置固定的旋转速度（每秒旋转的角度）
  // 调整这个值可以改变雷达旋转的速度
  const rotationSpeedPerSecond = 10; // 每秒旋转10度

  // 上次更新时间
  let lastTime = performance.now();

  // 使用requestAnimationFrame实现平滑动画
  const animate = (currentTime: number) => {
    // 计算时间差（毫秒）
    const deltaTime = currentTime - lastTime;
    lastTime = currentTime;

    // 获取当前时钟倍速
    const clockMultiplier = Number(speed.value) || 1;

    // 计算这一帧应该旋转的角度
    // 角度 = 每秒旋转角度 * 时钟倍速 * 时间差(秒)
    const rotationDelta =
      rotationSpeedPerSecond * clockMultiplier * (deltaTime / 1000);

    // 更新雷达旋转角度
    radarRotation.value = (radarRotation.value + rotationDelta) % 360;

    // 继续下一帧动画
    radarAnimationId.value = requestAnimationFrame(animate);
  };

  // 开始动画
  radarAnimationId.value = requestAnimationFrame(animate);
};

// 停止雷达旋转动画
const stopRadarAnimation = () => {
  if (radarAnimationId.value !== null) {
    cancelAnimationFrame(radarAnimationId.value);
    radarAnimationId.value = null;
  }
};

// 添加红军单位
const addRedForceUnits = (viewer: any, _startTime: any) => {
  // _startTime 参数目前未使用，但保留以便将来可能的扩展
  const positions: Cesium.Cartesian3[] = [];

  // 先添加所有单位
  RedForceUnits.forEach((unit) => {
    const position = Cesium.Cartesian3.fromDegrees(
      unit.position.lon,
      unit.position.lat,
      unit.position.alt,
    );

    positions.push(position);

    // 添加单位模型
    const entityId = "red_" + unit.id + "_" + uuid4();
    viewer.entities.add({
      id: entityId,
      name: unit.name,
      position: position,
      model: {
        uri: unit.model.uri,
        minimumPixelSize: unit.model.minimumPixelSize,
        maximumScale: unit.model.maximumScale,
        color: unit.model.color,
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
          0,
          10000000,
        ),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      },
      // 添加标签
      label: unit.label
        ? {
            text: unit.name,
            font: "14px sans-serif",
            fillColor: Cesium.Color.RED,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            pixelOffset: new Cesium.Cartesian2(0, -10),
            showBackground: true,
            backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.7),
            backgroundPadding: new Cesium.Cartesian2(7, 5),
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
          }
        : undefined,
      properties: {
        type: unit.type,
      },
    });

    redEntityId.value.push(entityId);

    // 如果是干扰雷达，添加扇形热力图效果
    if (unit.id === "redJammingRadar" && unit.radarRange) {
      // 创建扇形热力图效果
      const radarId = "radar_heatmap_" + uuid4();

      // 创建渐变材质
      const heatmapMaterial = new Cesium.ImageMaterialProperty({
        image: createSectorHeatmapImage(),
        transparent: true,
      });

      // 添加主要扇区（大扇区）
      viewer.entities.add({
        id: radarId,
        name: "干扰雷达热力图-主扇区",
        position: position,
        ellipse: {
          semiMajorAxis: unit.radarRange,
          semiMinorAxis: unit.radarRange,
          material: heatmapMaterial,
          height: 0,
          rotation: new Cesium.CallbackProperty(() => {
            return Cesium.Math.toRadians(radarRotation.value);
          }, false),
          stRotation: new Cesium.CallbackProperty(() => {
            return Cesium.Math.toRadians(radarRotation.value);
          }, false),
        },
      });

      radarEntityId.value.push(radarId);

      // 添加次要扇区（小扇区）
      const smallRadarId = "radar_heatmap_small_" + uuid4();
      const smallHeatmapMaterial = new Cesium.ImageMaterialProperty({
        image: createSmallSectorHeatmapImage(),
        transparent: true,
      });

      viewer.entities.add({
        id: smallRadarId,
        name: "干扰雷达热力图-次扇区",
        position: position,
        ellipse: {
          semiMajorAxis: unit.radarRange * 0.6, // 小一些的扇区
          semiMinorAxis: unit.radarRange * 0.6,
          material: smallHeatmapMaterial,
          height: 0,
          rotation: new Cesium.CallbackProperty(() => {
            // 次扇区旋转方向与主扇区相反
            return Cesium.Math.toRadians(radarRotation.value + 180);
          }, false),
          stRotation: new Cesium.CallbackProperty(() => {
            return Cesium.Math.toRadians(radarRotation.value + 180);
          }, false),
        },
      });

      radarEntityId.value.push(smallRadarId);

      // 默认不启动雷达旋转动画，只有在点击播放后才启动
      // 在初始化时不启动动画
    }
  });

  // 添加连接线 - 修改连接方式为图片中的样式
  // 连接红军指挥中心和坦克营
  const lineId1 = "connection_line1_" + uuid4();
  viewer.entities.add({
    id: lineId1,
    name: "红军指挥中心-坦克营连接线",
    polyline: {
      positions: [positions[0], positions[1]],
      width: 2,
      material: new Cesium.PolylineDashMaterialProperty({
        color: Cesium.Color.YELLOW,
        dashLength: 16.0,
      }),
      clampToGround: true,
    },
  });
  redEntityId.value.push(lineId1);

  // 连接红军指挥中心和空军指挥部
  const lineId2 = "connection_line2_" + uuid4();
  viewer.entities.add({
    id: lineId2,
    name: "红军指挥中心-空军指挥部连接线",
    polyline: {
      positions: [positions[0], positions[2]],
      width: 2,
      material: new Cesium.PolylineDashMaterialProperty({
        color: Cesium.Color.YELLOW,
        dashLength: 16.0,
      }),
      clampToGround: true,
    },
  });
  redEntityId.value.push(lineId2);

  // 连接坦克营和干扰雷达
  const lineId3 = "connection_line3_" + uuid4();
  viewer.entities.add({
    id: lineId3,
    name: "坦克营-干扰雷达连接线",
    polyline: {
      positions: [positions[1], positions[3]],
      width: 2,
      material: new Cesium.PolylineDashMaterialProperty({
        color: Cesium.Color.YELLOW,
        dashLength: 16.0,
      }),
      clampToGround: true,
    },
  });
  redEntityId.value.push(lineId3);

  // 移除空军指挥部和干扰雷达之间的连接线
  // const lineId4 = 'connection_line4_' + uuid4();
  // viewer.entities.add({
  //     id: lineId4,
  //     name: "空军指挥部-干扰雷达连接线",
  //     polyline: {
  //         positions: [positions[2], positions[3]],
  //         width: 2,
  //         material: new Cesium.PolylineDashMaterialProperty({
  //             color: Cesium.Color.YELLOW,
  //             dashLength: 16.0
  //         }),
  //         clampToGround: true
  //     }
  // });
  // redEntityId.value.push(lineId4);
};

// 添加蓝军飞机
const addBlueForceAircrafts = (viewer: any, startTime: any, stopTime: any) => {
  BlueForceAircrafts.forEach((aircraft) => {
    const dataPoint = aircraft.coordinates;
    let positionProperty = new Cesium.SampledPositionProperty();

    // 确保在初始时间点有位置数据
    const initialPosition = Cesium.Cartesian3.fromDegrees(
      dataPoint[0].lon,
      dataPoint[0].lat,
      dataPoint[0].alt,
    );

    // 添加初始位置样本
    positionProperty.addSample(startTime, initialPosition);

    // 添加后续位置样本
    for (let i = 0; i < dataPoint.length; i++) {
      let seconds = i * 240;
      // 确保第一个点的时间不是0，避免与startTime重复
      if (i === 0) seconds = 1;

      const time = Cesium.JulianDate.addSeconds(
        startTime,
        seconds,
        new Cesium.JulianDate(),
      );
      const position = Cesium.Cartesian3.fromDegrees(
        dataPoint[i].lon,
        dataPoint[i].lat,
        dataPoint[i].alt,
      );
      positionProperty.addSample(time, position);
    }

    const entityId = "blue_" + aircraft.id + "_" + uuid4();
    viewer.entities.add({
      id: entityId,
      name: aircraft.name,
      availability: new Cesium.TimeIntervalCollection([
        new Cesium.TimeInterval({
          start: startTime,
          stop: stopTime,
        }),
      ]),
      orientation: new Cesium.VelocityOrientationProperty(positionProperty),
      position: positionProperty,
      model: {
        uri: aircraft.model.uri,
        minimumPixelSize: aircraft.model.minimumPixelSize,
        maximumScale: aircraft.model.maximumScale,
        color: aircraft.model.color,
        distanceDisplayCondition: aircraft.model.distanceDisplayCondition,
      },
      // 确保飞机可见
      show: true,
      properties: {
        type: aircraft.type,
      },
    });

    blueEntityId.value.push(entityId);
  });
};

// 时钟控制操作
const clockControls = (type: any) => {
  const viewer = window.viewer;
  const clock = viewer.clock;
  const currentTime = clock.currentTime;
  switch (type) {
    case "start":
      clock.multiplier = 1;
      flag.value = "pause";
      clock.shouldAnimate = true;
      clock.currentTime = clock.startTime;
      redEntityId.value = blueEntityId.value = radarEntityId.value = [];
      init();
      // 确保在init()后再次设置shouldAnimate为true，因为init()可能会根据flag值设置它
      clock.shouldAnimate = true;
      // 启动雷达旋转动画
      startRadarAnimation();
      break;
    case "continue":
      flag.value = "pause";
      clock.shouldAnimate = true;
      // 继续雷达旋转动画
      startRadarAnimation();
      break;
    case "pause":
      flag.value = "continue";
      clearInterval(interval.value);
      clock.shouldAnimate = false;
      // 暂停雷达旋转动画
      stopRadarAnimation();
      break;
    case "reset":
      clearInterval(interval.value);
      speed.value = "1";
      clock.shouldAnimate = false;
      clock.currentTime = clock.startTime;
      flag.value = "start";
      // 停止雷达动画
      stopRadarAnimation();
      viewer.entities.values.forEach((entity: any) => {
        if (entity.model && entity.model.uri) {
          // 判断是否是带有模型的实体
          viewer.entities.removeAll();
        }
      });
      break;
    case "forward": //快进
      const nextTime = Cesium.JulianDate.addSeconds(
        currentTime,
        60,
        new Cesium.JulianDate(),
      );
      clock.currentTime = nextTime;
      break;
    case "retreat": //快退
      const lastTime = Cesium.JulianDate.addSeconds(
        currentTime,
        -60,
        new Cesium.JulianDate(),
      );
      clock.currentTime = lastTime;
      break;
    case "end": //结束
      clockControls("reset");
      speed.value = "1";
      main.value = false;
      flag.value = "start";
      interval.value = null;
      // 确保停止雷达动画
      stopRadarAnimation();
      redEntityId.value = [];
      blueEntityId.value = [];
      radarEntityId.value = [];
      bus.emit("initModel", { isShow: false });
      break;
    default:
      break;
  }
};

// 倍速变化
const changeSpeed = (value: any) => {
  const viewer = window.viewer;
  const clock = viewer.clock;
  clock.multiplier = Number(value);

  // 雷达动画速度会自动根据时钟倍速调整，不需要额外处理
};

defineExpose({
  init,
});

onUnmounted(() => {
  // 停止雷达动画
  stopRadarAnimation();
  clockControls("end");
});

// 监听关闭所有场景事件
bus.on("closeAllScenes", () => {
  if (main.value) {
    // 如果当前场景是打开的，则关闭它
    clockControls("end");
  }
});

bus.on("initModel", (res: any) => {
  if (res.isShow && res.type == "landAirCombat") {
    init();
    // 不自动启动雷达动画，保持静止状态
    stopRadarAnimation();
  } else if (!res.isShow && res.type == "landAirCombat") {
    clockControls("end");
  }
});

// 创建扇形热力图图像（主扇区）
const createSectorHeatmapImage = () => {
  const canvas = document.createElement("canvas");
  canvas.width = 1000;
  canvas.height = 1000;
  const ctx = canvas.getContext("2d");

  if (!ctx) {
    console.error("无法获取canvas 2d上下文");
    return canvas; // 返回空白canvas
  }

  // 清除画布
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // 设置扇形的中心点
  const centerX = canvas.width / 2;
  const centerY = canvas.height / 2;
  const radius = canvas.width / 2;

  // 扇形的起始角度和结束角度（以弧度表示）
  // 这里设置为90度的扇形（π/2弧度）
  const startAngle = -Math.PI / 4; // -45度
  const endAngle = Math.PI / 4; // 45度

  // 创建径向渐变
  const gradient = ctx.createRadialGradient(
    centerX,
    centerY,
    0,
    centerX,
    centerY,
    radius,
  );

  // 添加渐变色停止点 - 从中心蓝色到边缘红色
  gradient.addColorStop(0, "rgba(0, 0, 255, 0.8)"); // 中心蓝色
  gradient.addColorStop(0.25, "rgba(0, 255, 255, 0.7)"); // 青色
  gradient.addColorStop(0.5, "rgba(255, 255, 0, 0.6)"); // 黄色
  gradient.addColorStop(0.75, "rgba(255, 165, 0, 0.5)"); // 橙色
  gradient.addColorStop(1, "rgba(255, 0, 0, 0.4)"); // 边缘红色

  // 绘制扇形
  ctx.beginPath();
  ctx.moveTo(centerX, centerY);
  ctx.arc(centerX, centerY, radius, startAngle, endAngle);
  ctx.closePath();

  // 填充扇形
  ctx.fillStyle = gradient;
  ctx.fill();

  // 添加网格线效果
  drawRadarGrid(ctx, centerX, centerY, radius, startAngle, endAngle);

  return canvas;
};

// 创建小扇形热力图图像（次扇区）
const createSmallSectorHeatmapImage = () => {
  const canvas = document.createElement("canvas");
  canvas.width = 1000;
  canvas.height = 1000;
  const ctx = canvas.getContext("2d");

  if (!ctx) {
    console.error("无法获取canvas 2d上下文");
    return canvas; // 返回空白canvas
  }

  // 清除画布
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // 设置扇形的中心点
  const centerX = canvas.width / 2;
  const centerY = canvas.height / 2;
  const radius = canvas.width / 2;

  // 扇形的起始角度和结束角度（以弧度表示）
  // 这里设置为60度的扇形（π/3弧度）
  const startAngle = -Math.PI / 6; // -30度
  const endAngle = Math.PI / 6; // 30度

  // 创建径向渐变
  const gradient = ctx.createRadialGradient(
    centerX,
    centerY,
    0,
    centerX,
    centerY,
    radius,
  );

  // 添加渐变色停止点 - 从中心蓝色到边缘红色，但透明度更低
  gradient.addColorStop(0, "rgba(0, 0, 255, 0.6)"); // 中心蓝色
  gradient.addColorStop(0.25, "rgba(0, 255, 255, 0.5)"); // 青色
  gradient.addColorStop(0.5, "rgba(255, 255, 0, 0.4)"); // 黄色
  gradient.addColorStop(0.75, "rgba(255, 165, 0, 0.3)"); // 橙色
  gradient.addColorStop(1, "rgba(255, 0, 0, 0.2)"); // 边缘红色

  // 绘制扇形
  ctx.beginPath();
  ctx.moveTo(centerX, centerY);
  ctx.arc(centerX, centerY, radius, startAngle, endAngle);
  ctx.closePath();

  // 填充扇形
  ctx.fillStyle = gradient;
  ctx.fill();

  // 添加网格线效果
  drawRadarGrid(ctx, centerX, centerY, radius, startAngle, endAngle);

  return canvas;
};

// 绘制雷达网格线
const drawRadarGrid = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  radius: number,
  startAngle: number,
  endAngle: number,
) => {
  // 绘制同心圆
  const circleCount = 10; // 同心圆数量
  ctx.strokeStyle = "rgba(255, 255, 255, 0.3)";
  ctx.lineWidth = 1;

  for (let i = 1; i <= circleCount; i++) {
    const currentRadius = (radius * i) / circleCount;
    ctx.beginPath();
    ctx.arc(centerX, centerY, currentRadius, startAngle, endAngle);
    ctx.stroke();
  }

  // 绘制径向线
  const lineCount = 10; // 径向线数量
  const angleStep = (endAngle - startAngle) / lineCount;

  for (let i = 0; i <= lineCount; i++) {
    const angle = startAngle + angleStep * i;
    const endX = centerX + Math.cos(angle) * radius;
    const endY = centerY + Math.sin(angle) * radius;

    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.lineTo(endX, endY);
    ctx.stroke();
  }
};
</script>

<style lang="scss">
:deep(.speedSelect) {
  .el-select-dropdown__item {
    height: 24px !important;
    padding: 0 9px !important;
    font-size: 13px !important;
    line-height: 24px !important;
  }
}
</style>

<style scoped lang="scss">
#customContainer {
  position: absolute;
  z-index: 999;
  padding: 20px;

  .player {
    width: 300px;
    height: 40px;
    display: flex;
    border-radius: 5px 5px 0 0;
    align-items: center;
    background-color: rgba(240, 255, 255, 0.5);

    i {
      cursor: pointer;
    }

    :deep(.el-select.el-select--small.speed) {
      width: 50px;
      padding: 2px;

      .el-select__wrapper {
        background-color: #ffffff70;
      }

      .el-select__suffix,
      .el-input__suffix {
        display: none !important;
      }
    }
  }

  .info {
    border-radius: 0 0 5px 5px;
    background-color: rgba(240, 255, 255, 0.5);

    .title {
      font-size: 20px;
      font-weight: 900;
      text-align: start;
    }

    .con {
      text-align: start;
      padding-left: 15px;
    }
  }
}
</style>
