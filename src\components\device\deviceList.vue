<template>
    <div class="templateDiv" v-show="cameraDialog">
        <div class="close" @click="cameraDialog = false"></div>
        <div class="deviceList" id="deviceList">
            <div class="box" v-for="item in cameraList"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
import bus from '@/utils/bus'
const cameraDialog = ref(false);
const cameraList = ref([]);
bus.on('cameraList', (data: any) => {
    cameraList.value = data
    cameraDialog.value = true
    console.log("接收到了", data)
})
onMounted(() => {
    scrollInit()
})
// 初始化与绑定监听事件方法
const scrollInit = () => {
    // 获取要绑定事件的元素
    const nav = document.getElementById("deviceList")
    // 添加滚轮滚动监听事件
    nav?.addEventListener('mousewheel', handler, false)
    // 滚动事件的出来函数
    function handler(event: any) {
        // 获取滚动方向
        const detail = event.wheelDelta || event.detail;
        // 定义滚动方向，其实也可以在赋值的时候写
        const moveForwardStep = 1;
        const moveBackStep = -1;
        // 定义滚动距离
        let step = 0;
        // 判断滚动方向,这里的100可以改，代表滚动幅度，也就是说滚动幅度是自定义的
        if (detail < 0) {
            step = moveForwardStep * 100;
        } else {
            step = moveBackStep * 100;
        }
        // 对需要滚动的元素进行滚动操作
        nav.scrollLeft += step;
    }
}
</script>

<style scoped lang="scss">
.templateDiv {
    width: 100%;
    height: 300px;
    position: fixed;
    bottom: 0;
}

.deviceList {
    display: flex;
    height: 100%;
    width: 100%;
    overflow-x: scroll;
    white-space: nowrap;
    background: rgba(0, 0, 0, 0.3);

    .box {
        width: 260px;
        height: 260px;
        margin: 20px 10px 10px 10px;
        flex-shrink: 0;
        border-radius: 10px;
        border: 1px solid #fea111;
    }
}

.close {
    top: 0;
    right: 0;
    width: 16px;
    height: 16px;
    cursor: pointer;
    position: absolute;
    background: url("@/assets/images/icon/close.png") no-repeat;
}

::-webkit-scrollbar {
    /* 隐藏滚动条 */
    display: none;
}
</style>