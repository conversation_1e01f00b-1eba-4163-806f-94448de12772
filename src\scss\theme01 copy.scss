// src/scss/element-plus-theme.scss

:root {
  /* 主色调 */
  --el-color-primary: #00eaff; // 高亮青蓝色
  --el-color-primary-light-3: #1ad6ff !important;
  --el-color-primary-light-5: #2be2ff !important;
  --el-color-primary-light-7: #4ff6ff !important;
  --el-color-primary-dark-2: #0099b3 !important;
  /* 辅助色 */
  --el-color-success: #00ffb0 !important;
  --el-color-warning: #ffe066 !important;
  --el-color-danger: #ff4d4f !important;
  --el-color-info: #2b3a4b !important;

  /* 背景色 */
  --el-bg-color: #101c2c !important; // 深色背景
  --el-bg-color-page: #0a1420 !important; // 页面主背景
  --el-bg-color-overlay: #16263a !important; // 弹窗/浮层背景

  /* 边框色 */
  --el-border-color: #1a2a3a !important;
  --el-border-color-light: #22334a !important;

  /* 字体色 */
  --el-text-color-primary: #e6f7ff !important; // 主要文字
  --el-text-color-regular: #b2c9d6 !important;
  --el-text-color-secondary: #6fa7c9 !important;
  --el-text-color-placeholder: #4f6b8a !important;
  --el-text-color-disabled: #3a4d63 !important;

  /* 阴影 */
  --el-box-shadow: 0 2px 12px 0 rgba(0, 234, 255, 0.12) !important;

  /* 圆角 */
  --el-border-radius-base: 8px !important;
}