<script setup lang="ts">
import { ref, reactive } from "vue";
import * as Cesium from "cesium";
import pointHandler from "@/components/point/pointHadler.ts";

const pointArray = [
  {
    lon: 116.49680434957505,
    lat: 39.071276706876026,
    text: "摄像头1号",
    name: "摄像头1号",
    type: "cameraPoint",
  },
  {
    lon: 116.40094578266144,
    lat: 39.971751235972384,
    text: "摄像头2号",
    name: "摄像头2号",
    type: "cameraPoint",
  },
  {
    lon: 116.3902115821838,
    lat: 39.973329873303946,
    text: "摄像头3号",
    name: "摄像头3号",
    type: "cameraPoint",
  },
  {
    lon: 116.40033960342407,
    lat: 39.98044151119864,
    text: "摄像头4号",
    name: "摄像头4号",
    type: "cameraPoint",
  },
];

const addPoint = () => {
  pointArray.forEach((item) => {
    let obj = {
      position: Cesium.Cartesian3.fromDegrees(item.lon, item.lat),
      label: {
        text: item.text,
        font: "10px sans-serif", //字体样式
        fillColor: Cesium.Color.BLACK, //字体颜色
        backgroundColor: new Cesium.Color(19, 159, 159, 0.6), //背景颜色
        showBackground: true, //是否显示背景颜色
        style: Cesium.LabelStyle.FILL, //label样式
        outlineWidth: 2,
        verticalOrigin: Cesium.VerticalOrigin.CENTER, //垂直位置
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER, //水平位置
        pixelOffset: new Cesium.Cartesian2(0, 28), //偏移
        disableDepthTestDistance: 99000000, //不受深度的影响
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 30000000),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, // 设置该位置与地形相适应
      },
      type: item.type,
      name: "初始化摄像头",
      iconUrl: require("@/assets/images/point/camera.png"),
    };
    pointHandler.addOneIconPoint(obj);
  });
};
onMounted(() => {
  addPoint();
});
</script>
<template>
  <div class=""></div>
</template>
<style lang="scss" scoped></style>
