/**
 * 标记点类型常量
 */

// 标记点类型
export enum MarkerType {
  LANDMARK = "landmark",
  PARK = "park",
  EDUCATION = "education",
  TRANSPORTATION = "transportation",
  SHOPPING = "shopping",
  CULTURE = "culture",
  SPORTS = "sports",
}

// 标记点类型颜色映射
export const MarkerTypeColorMap: Record<MarkerType, string> = {
  [MarkerType.LANDMARK]: "#FF5722",
  [MarkerType.PARK]: "#4CAF50",
  [MarkerType.EDUCATION]: "#2196F3",
  [MarkerType.TRANSPORTATION]: "#9C27B0",
  [MarkerType.SHOPPING]: "#FFC107",
  [MarkerType.CULTURE]: "#795548",
  [MarkerType.SPORTS]: "#00BCD4",
};

// 标记点类型名称映射
export const MarkerTypeNameMap: Record<MarkerType, string> = {
  [MarkerType.LANDMARK]: "地标",
  [MarkerType.PARK]: "公园",
  [MarkerType.EDUCATION]: "教育",
  [MarkerType.TRANSPORTATION]: "交通",
  [MarkerType.SHOPPING]: "购物",
  [MarkerType.CULTURE]: "文化",
  [MarkerType.SPORTS]: "体育",
};

// 默认颜色
export const DEFAULT_MARKER_COLOR = "#3b7cff";
