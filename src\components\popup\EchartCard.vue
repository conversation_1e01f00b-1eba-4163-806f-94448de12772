<template>
  <el-dialog
    v-model="chartSelectionDialogVisible"
    title="选择图表类型"
    width="900px"
    append-to-body
    center
    :close-on-click-modal="true"
    :close-on-press-escape="true"
  >
    <div class="chart-selection-container">
      <div
        v-for="(chartName, chartType) in chartTypeNames"
        :key="chartType"
        class="chart-type-item"
        @click="selectChartType(chartType)"
      >
        <div class="chart-icon">
          <el-icon :size="36" color="#409eff">
            <component :is="getChartIconName(chartType)" />
          </el-icon>
        </div>
        <div class="chart-name">{{ chartName }}</div>
      </div>
    </div>

    <el-divider></el-divider>
    <div
      ref="chartRef"
      style="display: flex; justify-content: center; width: 100%; height: 400px"
    ></div>
    <!-- <el-empty v-if="!isChartInitialized" description="请选择图表类型" /> -->
  </el-dialog>
</template>

<script lang="ts" setup>
// 导入 Element Plus 图标
import {
  DataAnalysis,
  PieChart,
  Histogram,
  TrendCharts,
  Compass,
  Grid,
  Sort,
  Watch,
  Timer,
  Position,
  Operation,
} from "@element-plus/icons-vue";
import * as echarts from "echarts";

// 图表类型枚举
enum ChartType {
  PIE = "pie", // 饼图
  BASIC_BAR = "bar", // 基础柱状图
  BASIC_LINE = "line", // 基础折叠图
  HORIZONTAL_BAR = "hbar", // 条形图
  BASIC_AREA = "area", // 基础面积图
  STACKED_LINE = "sline", // 折线图堆叠
  LINE_AREA_HIGHLIGHT = "lareah", // 折线图区域高亮
  DOUGHNUT = "doughnut", // 环形图
  TIME_SERIES = "timeSeries", // 时间序列图
  SCATTER = "scatter", // 散点图
  RADAR = "radar", // 雷达图
}

// 图表类型名称映射
const chartTypeNames = {
  [ChartType.BASIC_BAR]: "基础柱状图",
  [ChartType.BASIC_LINE]: "基础折叠图",
  [ChartType.PIE]: "饼图",
  [ChartType.HORIZONTAL_BAR]: "条形图",
  [ChartType.BASIC_AREA]: "基础面积图",
  [ChartType.STACKED_LINE]: "折线图堆叠",
  [ChartType.LINE_AREA_HIGHLIGHT]: "折线图区域高亮",
  [ChartType.DOUGHNUT]: "环形图",
  [ChartType.TIME_SERIES]: "时间序列图",
  [ChartType.SCATTER]: "散点图",
  [ChartType.RADAR]: "雷达图",
};
// 定义图表数据类型
interface ChartDataType {
  [key: string]: any;
}

const charData = ref<ChartDataType>({});
const isChartInitialized = ref(false);

const close = () => {
  chartSelectionDialogVisible.value = false;
  // 关闭时重置图表状态
  if (chartIns.value) {
    chartIns.value.dispose();
    chartIns.value = null;
    isChartInitialized.value = false;
  }
};
const chartRef = ref<HTMLElement | null>(null);

const open = async (data: ChartDataType) => {
  // 重置图表状态
  isChartInitialized.value = false;

  // 打开对话框
  chartSelectionDialogVisible.value = true;

  // 保存图表数据
  charData.value = data;

  // 如果已有图表实例，销毁它
  if (chartIns.value) {
    chartIns.value.dispose();
    chartIns.value = null;
  }
  // 如果有数据，默认初始化饼图
  if (Object.keys(charData.value).length > 0 && charData.value[ChartType.PIE]) {
    nextTick(() => {
      selectChartType(ChartType.PIE);
    });
  }
};

defineExpose({
  open,
  close,
});
const chartIns = ref<echarts.EChartsType | null>(null);

// 获取图表图标名称
const getChartIconName = (chartType: ChartType) => {
  const iconMap: Record<string, any> = {
    [ChartType.PIE]: PieChart,
    [ChartType.BASIC_BAR]: Histogram,
    [ChartType.BASIC_LINE]: TrendCharts,
    [ChartType.HORIZONTAL_BAR]: DataAnalysis,
    [ChartType.BASIC_AREA]: Compass,
    [ChartType.STACKED_LINE]: Grid,
    [ChartType.LINE_AREA_HIGHLIGHT]: Watch,
    [ChartType.DOUGHNUT]: Sort,
    [ChartType.TIME_SERIES]: Timer,
    [ChartType.SCATTER]: Position,
    [ChartType.RADAR]: Operation,
  };
  return iconMap[chartType] || DataAnalysis;
};

const chartSelectionDialogVisible = ref(false);
const selectChartType = (chartType: ChartType) => {
  try {
    if (!chartRef.value) {
      console.error("图表容器元素不存在");
      return;
    }

    // 如果已有实例，先销毁
    if (!chartIns.value) {
      // chartIns.value.dispose();
      // 初始化图表
      chartIns.value = echarts.init(chartRef.value);
    }

    // 检查是否有对应类型的数据
    if (!charData.value[chartType]) {
      console.warn(`没有找到 ${chartType} 类型的图表数据`);
      isChartInitialized.value = false;
      return;
    }

    // 设置图表选项
    chartIns.value.setOption(charData.value[chartType], true);
    isChartInitialized.value = true;
  } catch (error) {
    console.error("初始化图表失败:", error);
    isChartInitialized.value = false;
  }
};
</script>

<style scoped>
.chart-selection-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 10px;

  .chart-type-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    background-color: #f5f7fa;

    &:hover {
      background-color: #e6f7ff;
      transform: translateY(-3px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .chart-icon {
      margin-bottom: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 50px;
    }

    .chart-name {
      font-size: 14px;
      color: #606266;
    }
  }
}
</style>
