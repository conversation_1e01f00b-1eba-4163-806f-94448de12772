import * as echarts from 'echarts';

/**
 * 生成简单的饼图
 * @returns 返回包含canvas元素的HTMLDivElement
 */
export function generatePieChart(): HTMLCanvasElement {
  // 创建一个容器
  const canvas = document.createElement('canvas');
  canvas.width = 100;
  canvas.height = 100;

  // 初始化ECharts实例
  const chart = echarts.init(canvas);

  // 配置图表选项
  const option = {
    animation: false,
    backgroundColor: 'rgba(0, 0, 0, 0)',
    tooltip: { show: false },
    legend: { show: false },
    series: [
      {
        name: '数据',
        type: 'pie',
        radius: '90%',
        center: ['50%', '50%'],
        data: [
          { value: 60, name: 'A', itemStyle: { color: '#5470c6' } },
          { value: 30, name: 'B', itemStyle: { color: '#91cc75' } },
          { value: 10, name: 'C', itemStyle: { color: '#fac858' } },
        ],
        label: {
          show: false
        },
        emphasis: {
          scale: false,
          itemStyle: {
            shadowBlur: 0,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0)'
          }
        }
      }
    ]
  };

  // 使用配置项设置图表
  chart.setOption(option);

  return canvas;
}

/**
 * 生成简单的柱状图
 * @returns 返回包含canvas元素的HTMLDivElement
 */
export function generateBarChart(): HTMLCanvasElement {
  // 创建一个容器
  const canvas = document.createElement('canvas');
  canvas.width = 100;
  canvas.height = 100;

  // 初始化ECharts实例
  const chart = echarts.init(canvas);

  // 配置图表选项
  const option = {
    animation: false,
    backgroundColor: 'rgba(0, 0, 0, 0)',
    tooltip: { show: false },
    legend: { show: false },
    grid: {
      top: 5,
      right: 5,
      bottom: 5,
      left: 5,
      containLabel: false
    },
    xAxis: {
      type: 'category',
      data: ['A', 'B', 'C'],
      show: false,
      axisLine: { show: false },
      axisTick: { show: false }
    },
    yAxis: {
      type: 'value',
      show: false,
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false }
    },
    series: [
      {
        data: [
          { value: 60, itemStyle: { color: '#5470c6' } },
          { value: 30, itemStyle: { color: '#91cc75' } },
          { value: 90, itemStyle: { color: '#fac858' } }
        ],
        type: 'bar',
        barWidth: '25',
        emphasis: {
          scale: false
        }
      }
    ]
  };

  // 使用配置项设置图表
  chart.setOption(option);

  return canvas;
}

/**
 * 根据图标类型生成对应的图表
 * @param type 图标类型
 * @returns 图表的canvas元素
 */
export function generateChartByType(type: string): HTMLCanvasElement | null {
  switch (type) {
    case 'pie':
      return generatePieChart();
    case 'bar':
      return generateBarChart();
    default:
      return null;
  }
}

/**
 * 将canvas转换为图片URL
 * @param canvas canvas元素
 * @returns 图片URL
 */
export function canvasToImageUrl(canvas: HTMLCanvasElement): string {
  return canvas.toDataURL('image/png');
}
