<template>
    <div id="customContainer" v-if="main">
        <div class="player">
            <el-tooltip content="快退" placement="bottom">
                <i class="iconfont font32 color999 gis-anniu_jiantouxiangzuo ml15"
                    @click="clockControls('retreat')"></i>
            </el-tooltip>
            <el-tooltip v-if="flag == 'start'" content="开始" placement="bottom">
                <i class="iconfont font32 color999 gis-bofang" @click="clockControls('start')"></i>
            </el-tooltip>
            <el-tooltip v-if="flag == 'continue'" content="继续" placement="bottom">
                <i class="iconfont font32 color999 gis-bofang" @click="clockControls('continue')"></i>
            </el-tooltip>
            <el-tooltip v-if="flag == 'pause'" content="暂停" placement="bottom">
                <i class="iconfont font32 color999 gis-zanting" @click="clockControls('pause')"></i>
            </el-tooltip>

            <el-tooltip content="快进" placement="bottom">
                <i class="iconfont font32 color999 gis-anniu-jiantouxiangyou" @click="clockControls('forward')"></i>
            </el-tooltip>
            <el-tooltip content="重置" placement="bottom">
                <i class="iconfont font32 color-warn gis-tingzhi" @click="clockControls('reset')"></i>
            </el-tooltip>
            <el-tooltip content="关闭演示" placement="bottom">
                <i class="iconfont font32 color-danger gis-anniu_guanbi" @click="clockControls('end')"></i>
            </el-tooltip>
            <span style="font-size: 13px;margin-left: 10px;">倍速：</span>
            <el-select v-model="speed" size="small" class="speed" @change="changeSpeed" popper-class="speedSelect">
                <el-option key="1" label="1X" value="1" />
                <el-option key="2" label="2X" value="2" />
                <el-option key="4" label="4X" value="4" />
                <el-option key="8" label="8X" value="8" />
                <el-option key="16" label="16X" value="16" />
                <el-option key="32" label="32X" value="32" />
                <el-option key="64" label="64X" value="64" />
                <el-option key="128" label="128X" value="128" />
            </el-select>
        </div>
        <div class="info pb15">
            <div class="weather pl10 mb10">
                <div class="title">天气信息</div>
                <div class="con">天气：🌤 多云转晴</div>
                <div class="con">温度：9 ~ 18 ℃</div>
                <div class="con">湿度：23%</div>
                <div class="con">风速：5m/s</div>
                <div class="con">气压：1023hPa</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";
import { DemoCoordinates } from "@/components/yanshi/data.js";
import { v4 as uuid4 } from "uuid";
import bus from "~/src/utils/bus";

const main = ref(false)
const flag = ref('start')
const speed = ref('1')
const interval = ref()
const redEntityId = ref([])
const missileEntityId = ref([]) //弹


// 一、初始化地图与viewer.clock时间配置
const init = async () => {
    main.value = true
    const viewer = window.viewer
    viewer.entities.removeAll()
    const startTime = Cesium.JulianDate.fromDate(new Date());
    const stopTime = Cesium.JulianDate.addSeconds(startTime, 3600, new Cesium.JulianDate());
    const viewCenter = DemoCoordinates[0]
    viewer.clock.startTime = startTime.clone();
    viewer.clock.stopTime = stopTime.clone();
    viewer.clock.multiplier = 20;
    viewer.clock.clockRange = Cesium.ClockRange.CLAMPED;
    viewer.timeline.zoomTo(startTime, stopTime);
    viewer.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(viewCenter.lon, viewCenter.lat, 800000),
        orientation: {
            heading: Cesium.Math.toRadians(0),
            pitch: Cesium.Math.toRadians(-90),
            roll: 0,
        },
    });

    let leidaEntity = viewer.entities.add({
        id: 'leida' + uuid4(),
        name: "探测雷达",
        position: Cesium.Cartesian3.fromDegrees(viewCenter.lon, viewCenter.lat),
        model: {
            scale: 0.6,
            uri: "src/assets/models/3.glb",
            minimumPixelSize: 30,
            maximumScale: 100,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 7000000),
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        ellipse: {
            height: 0,
            outline: true,
            outlineWidth: 15,
            semiMajorAxis: 30000,
            semiMinorAxis: 30000,
            material: Cesium.Color.WHITE.withAlpha(0.1),
            outlineColor: Cesium.Color.fromCssColorString("#59FF9B"),
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 7000000),
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
    })
    for (let j = 1; j < 3; j++) {
        const dataPoint = DemoCoordinates[j];
        viewer.entities.add({
            id: dataPoint.id,
            name: dataPoint.name,
            position: Cesium.Cartesian3.fromDegrees(dataPoint.coordinates.lon, dataPoint.coordinates.lat, 10),
            model: {
                uri: dataPoint.uri,
                minimumPixelSize: dataPoint.minimumPixelSize,
                maximumScale: dataPoint.maximumScale,
                color: dataPoint.color,
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 400000),
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            },
        })
    }

    //二、Entity实体加载与路线绘制
    for (let j = 3; j < DemoCoordinates.length; j++) {
        const entityData = DemoCoordinates[j];
        const dataPoint = entityData.coordinates;
        let positionProperty = new Cesium.SampledPositionProperty();
        for (let i = 0; i < dataPoint.length; i++) {

            let seconds: number = entityData.type == 'missile' ? (i + 4) * 290 : entityData.id.includes('car') || entityData.id.includes('tank') ? (i + 3) * 300 : i * 360;
            const time = Cesium.JulianDate.addSeconds(startTime, seconds, new Cesium.JulianDate());
            const position = Cesium.Cartesian3.fromDegrees(dataPoint[i].lon, dataPoint[i].lat, dataPoint[i].alt);

            positionProperty.addSample(time, position);
        }

        let entity = viewer.entities.add({
            id: entityData.id,
            name: entityData.id,
            availability: new Cesium.TimeIntervalCollection([
                new Cesium.TimeInterval({
                    start: startTime,
                    stop: stopTime,
                }),
            ]),
            orientation: new Cesium.VelocityOrientationProperty(positionProperty),
            position: positionProperty,
            model: {
                scale: 1,
                uri: entityData.uri,
                minimumPixelSize: entityData.minimumPixelSize,
                maximumScale: entityData.maximumScale,
                color: entityData.color,
                distanceDisplayCondition: entityData.distanceDisplayCondition,
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            },
            properties: {
                type: entityData.type
            },
        });

    }
};
// 时钟控制操作
const clockControls = (type: any) => {
    const viewer = window.viewer;
    const clock = viewer.clock;
    const currentTime = clock.currentTime;
    switch (type) {
        case "start":
            clock.multiplier = 1
            flag.value = 'pause'
            clock.shouldAnimate = true
            clock.currentTime = clock.startTime;
            redEntityId.value = missileEntityId.value = []
            init()
            break;
        case "continue":
            flag.value = 'pause'
            clock.shouldAnimate = true;
            break;
        case "pause":
            flag.value = 'continue'
            clearInterval(interval.value)
            clock.shouldAnimate = false
            break;
        case "reset":
            clearInterval(interval.value)
            speed.value = '1'
            clock.shouldAnimate = false
            clock.currentTime = clock.startTime;
            flag.value = 'start'
            viewer.entities.values.forEach((entity) => {
                if (entity.model && entity.model.uri) { // 判断是否是带有模型的实体
                    viewer.entities.removeAll();
                }
            });
            break;
        case "forward": //快进
            const nextTime = Cesium.JulianDate.addSeconds(currentTime, 60, new Cesium.JulianDate())
            clock.currentTime = nextTime;
            break;
        case "retreat": //快退
            const lastTime = Cesium.JulianDate.addSeconds(currentTime, -60, new Cesium.JulianDate())
            clock.currentTime = lastTime;
            break;
        case "end": //结束
            clockControls('reset')
            speed.value = '1'
            main.value = false
            flag.value = 'start'
            interval.value = null
            redEntityId.value = []
            missileEntityId.value = []
            bus.emit("initModel", { isShow: false })
            break;
        default:
            break;
    }
}
const changeSpeed = (value: any) => {
    const viewer = window.viewer;
    const clock = viewer.clock;
    clock.multiplier = Number(value)

}
defineExpose({
    init
});
onUnmounted(() => {
    clockControls('end')
})

// 监听初始化事件
bus.on("initModel", (res: any) => {
    if (res.isShow && res.type == 'yanshi') {
        init()
    } else if (!res.isShow && res.type == 'yanshi') {
        clockControls('end')
    }
})

// 监听关闭所有场景事件
bus.on("closeAllScenes", () => {
    if (main.value) {
        // 如果当前场景是打开的，则关闭它
        clockControls('end')
    }
})
</script>
<style lang="scss">
:deep(.speedSelect) {
    .el-select-dropdown__item {
        height: 24px !important;
        padding: 0 9px !important;
        font-size: 13px !important;
        line-height: 24px !important;
    }
}
</style>

<style scoped lang="scss">
#customContainer {
    position: absolute;
    z-index: 999;
    padding: 20px;

    .player {
        width: 300px;
        height: 40px;
        display: flex;
        border-radius: 5px 5px 0 0;
        align-items: center;
        background-color: rgba(240, 255, 255, 0.5);

        i {
            cursor: pointer;
        }

        :deep(.el-select.el-select--small.speed) {
            width: 50px;
            padding: 2px;

            .el-select__wrapper {
                background-color: #ffffff70
            }

            .el-select__suffix,
            .el-input__suffix {
                display: none !important;
            }
        }

    }

    .info {
        border-radius: 0 0 5px 5px;
        background-color: rgba(240, 255, 255, 0.5);

        .title {
            font-size: 20px;
            font-weight: 900;
            text-align: start;
        }

        .con {
            text-align: start;
            padding-left: 15px;
        }

    }

    #buttonContainer {
        margin-bottom: 5px;

        #trackViewButton {
            margin: 0 10px;
        }
    }
}
</style>
