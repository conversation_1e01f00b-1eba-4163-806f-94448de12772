<template>
    <div class="dodaf" v-if="flag">
        <div class="title">
            <span class="float-left ml40">路径规划</span>
            <span class="red float-right mr6 cursor-pointer" @click="flag=false">
                <i class="iconfont gis-shanchu"></i>
            </span>
        </div>
        <div class="tree">
            <el-tree style="max-width: 600px" :data="data"  @node-click="handleNodeClick" />
        </div>
    </div>
</template>

<script setup lang="ts">
interface Tree {
    label: string
    children?: Tree[]
}
const flag = ref(false);

const handleNodeClick = (data: Tree) => {
    console.log(data)
}

const data: Tree[] = [
    {
        label: "所有图",
        children: [
            {
                label: "CV-2",
                id: "node-2",
            },
            {
                label: "OV-1",
                children: [
                    {
                        label: "OV-1 高级作战概念图",
                        id: "node-5",
                    },
                    {
                        label: "OV-2",
                        children: [
                            {
                                label: "OV-2 作战资源流描述",
                                id: "node-7",
                            },
                            {
                                label: "0V-4",
                                children: [
                                    {
                                        label: "0V-4 组织关系图",
                                        id: "node-9",
                                    },
                                    {
                                        label: "0V-5a",
                                        children: [
                                            {
                                                label: "0V-5a 能力分类",
                                                id: "node-11",
                                            },
                                            {
                                                label: "OV-5b",
                                                children: [
                                                    {
                                                        label: "OV-5b 能力分类",
                                                        id: "node-13",
                                                    },
                                                ],
                                                id: "node-12",
                                            },
                                        ],
                                        id: "node-10",
                                    },
                                ],
                                id: "node-8",
                            },
                        ],
                        id: "node-6",
                    },
                    {
                        label: "OV-5c",
                        children: [
                            {
                                label: "OV-5c 能力分类",
                                id: "node-15",
                            },
                        ],
                        id: "node-14",
                    },
                ],
                id: "node-4",
            },
        ],
        id: "node-1",
    },
];

const close = () => {

}

const defaultProps = {
    children: 'children',
    label: 'label',
}
</script>

<style lang="scss" scoped>
.dodaf {
    // height: 100%;
    height: 800px;
    width: 300px;
    grid-row: 1;
    overflow: hidden;
    color: var(--gis-default-color);
    // background-image: linear-gradient(to right, rgba(23, 44, 63, 0.35), rgba(23, 44, 63, 0.35));
    background-image: linear-gradient(to right, rgba(15, 57, 115, 0.6), rgba(15, 57, 115, 0.6));

    :deep(.el-tree) {
        background: transparent;
        color: aliceblue;

        .el-tree-node:focus>.el-tree-node__content,
        .el-tree-node__content:hover {
            background-color: #448AFF;
        }
    }

    .tree {
        padding: 10px 15px;
        width: calc(100% - 30px);
        height: calc(100% - 20px);
    }
    .title {
        width: 100%;
        height: 26px;
        background-size: 100% 100%;
        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAAARCAYAAADzJPoFAAABrklEQVR4nO3cz0obURSA8S/BVtF3KG2lW/e6deFCaN30JUpRjJVY6NKCJUL6x7foposuSqG48xkqLgTxEapUXEQunMAwJJmbpdfvt5wZkjmZA4d7zp20lg8GAD+BdZp9BD4AO8Bh5epbYAn4O+kTTroZ3yBJuhfacZPbwP+MG34HvAC+1orFI+DIRy5JD8ewgJwBvYyoZ4F+rDje1s6tAq/NHUl6GNqVKA+Ai4yoU6vrFfAH+F47l9paC+aOJJWvWkCuga3MiNMqZC5aWleV409iRiJJKly7Ft4P4HdGyM+AvVix7NfObcecRJJUsHoBSTaBm4yQu1FI+jFDGUpzkm8mjSSVbVQBOY2i0CS1sD5HsdmsXbsGbJg7klSuUQWEeN/jMiPqlzFU/xXtr6pUhObNHUkq07gC8g/oZEbcj7ZVJwbxQ09jTiJJKtC4AkJs0T3OCDkNzHeB89gKXJWOL5o4klSeSQUkeRMvDTZ5H1t4e1FIhtKc5It5I0nlmWmIKP1dyeMpo35unkhS+VqDwcDHLEmaWlMLS5KkkWZWPvnDSJKml2Yg9rAkSdMB7gC1QTnm9AlvDgAAAABJRU5ErkJggg==);
    }

}
</style>