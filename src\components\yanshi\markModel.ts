import { svgCode } from "@/components/point/svgSource";
import * as Cesium from "cesium";
import tool from "../tool/tool";
import { ModelPath } from "@/store/modelPath.ts";

class MarkModel {

  static addModel(type: any) {
    const viewer: Cesium.Viewer = window.viewer;
    const svg = svgCode[type];
    const storeId = ModelPath().id;
    const store = ModelPath().demoData.filter((el)=>el.id==storeId)[0]; 
    
    const ids = new Date().getTime();

    tool.cesiumCursorConvert("pointer"); 
    if (viewer) {
      let handlers = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
      let images = '@/assets/images/model/'+type+'.png'
      handlers.setInputAction(function (movement: any) {
        var cartesian = tool.getCatesian3FromPX(movement.position);
        let pointEntity = new Cesium.Entity({
          id: ids.toString(),
          name: svg.name,
          position: cartesian,
          label: {
            text: svg.name,
            show: true,
            font: "11px serif bold",
            distanceDisplayCondition: svg.distanceDisplayCondition,
            pixelOffset: new Cesium.Cartesian2(0, 35), 
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            disableDepthTestDistance: Number.POSITIVE_INFINITY, 
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, 
          },
          billboard: {
            image: require("@/assets/images/model/target.png"),
            // image: require(images),
            // image: svg.value || require("@/assets/images/point/camera.png"),
            width: 48,
            height: 48,
            scale: 1.0,
            sizeInMeters: false,
            // color:Cesium.Color.RED,
            verticalOrigin: Cesium.VerticalOrigin.CENTER,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            distanceDisplayCondition: svg.distanceDisplayCondition, 
            disableDepthTestDistance: 10000,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            show: true,
          },
          properties: {
            position: cartesian,
            type: type,
            isRightMenu: true, 
            isEdit: true,
            svg: svg,
            isEditPoint: svg.isEditPoint,
          },
        });
        viewer.entities.add(pointEntity);
        handlers.destroy();
        tool.cesiumCursorConvert("default"); 
        tool.mouseTooltip({}, true);
        if (!svg.isEditPoint) {
          let position = tool.transformCartesianToWGS84(cartesian);
          let obj = svg;
          obj.id = ids;
          obj.coordinates = {lon:position?.lon,lat:position?.lat,alt:0};
          if (svg.name == '防护目标') {
            store.target.push(obj);
          } else {
            store.definedEquipment.push(obj);
          }
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      handlers.setInputAction(function (movement: { endPosition: any }) {
        var cartesian = tool.getCatesian3FromPX(movement.endPosition);
        const obj = { position: cartesian, text: "请选择部署点位" };
        tool.mouseTooltip(obj);
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

      handlers.setInputAction(function (movement: any) {
        handlers.destroy();
        tool.cesiumCursorConvert("default"); 
        tool.mouseTooltip({}, true);
      }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }
  }
}
export default MarkModel;
