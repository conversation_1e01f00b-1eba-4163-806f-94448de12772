<template>
    <div id="gisRightMenu" class="gisRightMenu" v-if="menuFlag" :style="popupStyle">
        <div class="menuRow" @click="delEntity">
            <span class="icon"> <i class="iconfont font18 gis-shanchu1"></i></span>
            <span class="text"> 删 除</span>
        </div>
        <div class="menuRow" v-if="edit" @click="lockEntity(false)">
            <span class="icon"> <i class="icon iconfont font18 gis-suoding"></i></span>
            <span class="text"> 锁 定</span>
        </div>
        <div class="menuRow" v-if="!edit && entity?.properties?.hasOwnProperty('isEdit')" @click="lockEntity(true)">
            <span class="icon"> <i class="icon iconfont font18 gis-jiesuo"></i></span>
            <span class="text"> 解 锁</span>
        </div>
        <div class="menuRow" v-if="edit" @click="editEntity">
            <span class="icon"> <i class="icon iconfont font18 gis-bianji1"></i></span>
            <span class="text"> 编 辑</span>
        </div>
        <div class="menuRow" v-if="path" @click="addPath">
            <span class="icon"> <i class="icon iconfont font18 gis-dingwei"></i></span>
            <span class="text"> 路径规划</span>
        </div>
        <!-- <div class="menuRow"></div> -->
    </div>
</template>

<script lang="ts" setup>
import bus from '~/src/utils/bus';
import * as Cesium from "cesium";
import tool from '../tool/tool';

const entity = ref();
const edit = ref(false);
const path = ref(false);
const isShow = ref(false);
const menuFlag = ref(false);
const popupStyle = reactive({
    top: '100px',  // 初始的top值，可以按需调整
    left: '100px'  // 初始的left值，可以按需调整
});
// 获取实体数据
const initEntity = (param: any) => {
    const viewer = window.viewer;
    let handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    popupStyle.left = param.cartesian2.x + 10 + 'px'
    popupStyle.top = param.cartesian2.y + 10 + 'px'
    entity.value = param.entity
    edit.value = param.entity?.properties?.isEdit?._value
    path.value = param.entity?.properties?.isEditPoint?._value || false
    menuFlag.value = true
    handler.setInputAction(function (current: any) {
        let pickedObject = viewer.scene.pick(current.position), move = false
        if (Cesium.defined(pickedObject) && pickedObject.id instanceof Cesium.Entity && pickedObject.id.id == entity.value.id) {
            // console.log("点击着呢呢呢呢=>", pickedObject.id);
            move = true
        }
        handler.setInputAction(function (movement: any) {
            handler.setInputAction(function () {
                move = false
            }, Cesium.ScreenSpaceEventType.LEFT_UP)
            var cartesian = tool.getCatesian3FromPX(movement.endPosition);
            if (move && isShow.value) {
                viewer.scene.screenSpaceCameraController.enableRotate = false;
                entity.value.position = cartesian;
                bus.emit("editPointEntity", { entity: entity.value, isShow: isShow.value })
            } else {
                move = false
                viewer.scene.screenSpaceCameraController.enableRotate = true;
                handler.destroy();
                close();
            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
}
// 删除实体类
const delEntity = () => {
    const viewer = window.viewer;
    viewer.entities.remove(entity.value);
    close()
}

const lockEntity = (param: boolean) => {
    entity.value.properties.isEdit = param
    edit.value = param
    close()
}
// 关闭右键菜单
const close = () => {
    const viewer = window.viewer
    entity.value = null
    menuFlag.value = isShow.value = false
    console.log("关闭.....");
    bus.emit("editPointEntity", { entity: entity.value, isShow: isShow.value })
}
// 编辑实体类
const editEntity = () => {
    menuFlag.value = false
    isShow.value = true
    bus.emit("editPointEntity", { entity: entity.value, isShow: isShow.value })
    bus.emit("selectPointEntity", { entity: entity.value, isShow: false })
}
// 规划模型路径
const addPath = () => {
    menuFlag.value = false
    bus.emit("selectPointEntity", { entity: entity.value, isShow: true })
    bus.emit("editPointEntity", { entity: entity.value, isShow: false })
}
// bus.on("mouseMove", () => { if (menuFlag.value) close(); })
bus.on("mouseWheel", () => { if (menuFlag.value) close() })
bus.on("editEntity", (res: any) => { if (res.entity?.properties?.isRightMenu?._value) initEntity(res) });

</script>

<style lang="scss" scoped>
.gisRightMenu {
    position: fixed;
    top: 300px;
    right: 500px;
    padding: 5px;
    width: 90px;
    border-radius: 6px;
    background: #212121;

    .menuRow {
        font-size: 13px;
        color: #F5F5F5;
        height: 24px;
        display: flex;
        border-radius: 3px;
        align-items: center;
        line-height: 24px;
        cursor: pointer;

        >i {
            color: #F5F5F5;
        }

        .icon {
            width: 24px;
        }

        &:active,
        &:hover {
            // color: #3b7cff;
            background: #448AFF;
        }
    }
}
</style>